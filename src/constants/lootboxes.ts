import banana from '@/assets/images/temp/big-icons/bananaBox.png'
import combat from '@/assets/images/temp/big-icons/combatBox.png'
import gift from '@/assets/images/temp/big-icons/giftBox.png'
import hustle from '@/assets/images/temp/big-icons/hustleBox.png'
import infinity from '@/assets/images/temp/big-icons/infinityBox.png'
import justice from '@/assets/images/temp/big-icons/justiceBox.png'
import kungFu from '@/assets/images/temp/big-icons/kungFuBox.png'
import lucky from '@/assets/images/temp/big-icons/luckyBox.png'
import madagascar from '@/assets/images/temp/big-icons/madagascarBox.png'
import minecraft from '@/assets/images/temp/big-icons/minecraftBox.png'
import rainbow from '@/assets/images/temp/big-icons/rainbowBox.png'
import scooby from '@/assets/images/temp/big-icons/scoobyBox.png'
import shrek from '@/assets/images/temp/big-icons/shrekBox.png'
import sleepa from '@/assets/images/temp/big-icons/sleepaBox.png'
import sponge from '@/assets/images/temp/big-icons/spongeBox.png'
import starWars from '@/assets/images/temp/big-icons/starWarsBox.png'
import throne from '@/assets/images/temp/big-icons/throneBox.png'

import shineBlue from '@/assets/images/temp/big-icons/shine-blue.png'
import shineGold from '@/assets/images/temp/big-icons/shine-gold.png'
import shinePink from '@/assets/images/temp/big-icons/shine-pink.png'

import luckyExplosion from '@/assets/images/temp/lucky-explosion.png'
import rainbowExplosion from '@/assets/images/temp/rainbow-explosion.png'

import aim from '@/assets/images/temp/big-icons/aim.png'
import hard from '@/assets/images/temp/big-icons/hard.png'
import jumper from '@/assets/images/temp/big-icons/jumper.png'
import horn from '@/assets/images/temp/big-icons/magic-horn.png'
import magnet from '@/assets/images/temp/big-icons/magnet.png'
import soft from '@/assets/images/temp/big-icons/soft.png'
import wheelSpin from '@/assets/images/temp/big-icons/wheel-spin.png'

import tickets from '@/assets/images/temp/big-icons/ticket.png'
import unlimitedLives from '@/assets/images/temp/big-icons/unlimited-lives.png'

import skin2000 from '@/assets/images/temp/skins/2000.png'
import skin2001 from '@/assets/images/temp/skins/2001.png'
import skin2002 from '@/assets/images/temp/skins/2002.png'
import skin2003 from '@/assets/images/temp/skins/2003.png'
import skin2004 from '@/assets/images/temp/skins/2004.png'
import skin2005 from '@/assets/images/temp/skins/2005.png'
import skin2006 from '@/assets/images/temp/skins/2006.png'
import skin2007 from '@/assets/images/temp/skins/2007.png'
import skin2008 from '@/assets/images/temp/skins/2008.png'
import skin2009 from '@/assets/images/temp/skins/2009.png'
import skin2010 from '@/assets/images/temp/skins/2010.png'
import skin2011 from '@/assets/images/temp/skins/2011.png'
import skin2015 from '@/assets/images/temp/skins/2015.png'
import skin2016 from '@/assets/images/temp/skins/2016.png'
import skin2017 from '@/assets/images/temp/skins/2017.png'
import skin2018 from '@/assets/images/temp/skins/2018.png'
import skin2019 from '@/assets/images/temp/skins/2019.png'
import skin2020 from '@/assets/images/temp/skins/2020.png'
import skin2021 from '@/assets/images/temp/skins/2021.png'
import skin2022 from '@/assets/images/temp/skins/2022.png'
import skin2023 from '@/assets/images/temp/skins/2023.png'
import skin2024 from '@/assets/images/temp/skins/2024.png'
import skin2025 from '@/assets/images/temp/skins/2025.png'
import skin2026 from '@/assets/images/temp/skins/2026.png'
import skin2027 from '@/assets/images/temp/skins/2027.png'
import skin2028 from '@/assets/images/temp/skins/2028.png'
import skin2029 from '@/assets/images/temp/skins/2029.png'
import skin2030 from '@/assets/images/temp/skins/2030.png'
import skin2031 from '@/assets/images/temp/skins/2031.png'
import skin2032 from '@/assets/images/temp/skins/2032.png'
import skin2034 from '@/assets/images/temp/skins/2034.png'
import skin2035 from '@/assets/images/temp/skins/2035.png'
import skin2036 from '@/assets/images/temp/skins/2036.png'
import skin2037 from '@/assets/images/temp/skins/2037.png'
import skin2038 from '@/assets/images/temp/skins/2038.png'
import skin2040 from '@/assets/images/temp/skins/2040.png'
import skin2041 from '@/assets/images/temp/skins/2041.png'
import skin2042 from '@/assets/images/temp/skins/2042.png'
import skin2043 from '@/assets/images/temp/skins/2043.png'
import skin2044 from '@/assets/images/temp/skins/2044.png'
import skin2045 from '@/assets/images/temp/skins/2045.png'
import skin2047 from '@/assets/images/temp/skins/2047.png'
import skin2048 from '@/assets/images/temp/skins/2048.png'
import skin2049 from '@/assets/images/temp/skins/2049.png'
import skin2050 from '@/assets/images/temp/skins/2050.png'
import skin2051 from '@/assets/images/temp/skins/2051.png'
import skin2052 from '@/assets/images/temp/skins/2052.png'
import skin2053 from '@/assets/images/temp/skins/2053.png'
import skin2054 from '@/assets/images/temp/skins/2054.png'
import skin2055 from '@/assets/images/temp/skins/2055.png'
import skin2056 from '@/assets/images/temp/skins/2056.png'
import skin2057 from '@/assets/images/temp/skins/2057.png'
import skin2058 from '@/assets/images/temp/skins/2058.png'
import skin2061 from '@/assets/images/temp/skins/2061.png'
import skin2062 from '@/assets/images/temp/skins/2062.png'
import skin2063 from '@/assets/images/temp/skins/2063.png'
import skin2064 from '@/assets/images/temp/skins/2064.png'
import skin2065 from '@/assets/images/temp/skins/2065.png'
import skin2066 from '@/assets/images/temp/skins/2066.png'
import skin2068 from '@/assets/images/temp/skins/2068.png'
import skin2069 from '@/assets/images/temp/skins/2069.png'
import skin2070 from '@/assets/images/temp/skins/2070.png'
import skin2071 from '@/assets/images/temp/skins/2071.png'
import skin2072 from '@/assets/images/temp/skins/2072.png'
import skin2073 from '@/assets/images/temp/skins/2073.png'
import skin2075 from '@/assets/images/temp/skins/2075.png'
import skin2076 from '@/assets/images/temp/skins/2076.png'
import skin2077 from '@/assets/images/temp/skins/2077.png'
import skin2078 from '@/assets/images/temp/skins/2078.png'
import skin2079 from '@/assets/images/temp/skins/2079.png'
import skin2080 from '@/assets/images/temp/skins/2080.png'
import skin2082 from '@/assets/images/temp/skins/2082.png'
import skin2083 from '@/assets/images/temp/skins/2083.png'
import skin2084 from '@/assets/images/temp/skins/2084.png'
import skin2085 from '@/assets/images/temp/skins/2085.png'
import skin2086 from '@/assets/images/temp/skins/2086.png'
import skin2087 from '@/assets/images/temp/skins/2087.png'
import skin2089 from '@/assets/images/temp/skins/2089.png'
import skin2090 from '@/assets/images/temp/skins/2090.png'
import skin2091 from '@/assets/images/temp/skins/2091.png'
import skin2092 from '@/assets/images/temp/skins/2092.png'
import skin2093 from '@/assets/images/temp/skins/2093.png'
import skin2094 from '@/assets/images/temp/skins/2094.png'
import skin2096 from '@/assets/images/temp/skins/2096.png'
import skin2097 from '@/assets/images/temp/skins/2097.png'
import skin2098 from '@/assets/images/temp/skins/2098.png'
import skin2099 from '@/assets/images/temp/skins/2099.png'
import skin2100 from '@/assets/images/temp/skins/2100.png'
import skin2101 from '@/assets/images/temp/skins/2101.png'
import skin2102 from '@/assets/images/temp/skins/2102.png'
import skin2106 from '@/assets/images/temp/skins/2106.png'
import skin2107 from '@/assets/images/temp/skins/2107.png'
import skin2108 from '@/assets/images/temp/skins/2108.png'
import skin2112 from '@/assets/images/temp/skins/2112.png'
import skin2113 from '@/assets/images/temp/skins/2113.png'
import skin2114 from '@/assets/images/temp/skins/2114.png'
import skin2118 from '@/assets/images/temp/skins/2118.png'
import skin2119 from '@/assets/images/temp/skins/2119.png'
import skin2120 from '@/assets/images/temp/skins/2120.png'
import skin2124 from '@/assets/images/temp/skins/2124.png'
import skin2125 from '@/assets/images/temp/skins/2125.png'
import skin2126 from '@/assets/images/temp/skins/2126.png'
import skin2130 from '@/assets/images/temp/skins/2130.png'
import skin2131 from '@/assets/images/temp/skins/2131.png'
import skin2132 from '@/assets/images/temp/skins/2132.png'

import type { LootBoxType, RewardType } from '@/services/openapi'

export const LOOTBOX_TYPE_TO_IMAGE: Record<LootBoxType, string> = {
  rainbowLootBox: rainbow,
  luckyLootBox: lucky,
  cryptoLootBox: lucky,
  moonLootBox: lucky,
  easterLootBox: lucky,
  minecraftLootBox: minecraft,
  bananaLootBox: banana,
  starWarsLootBox: starWars,
  infinityLootBox: infinity,
  kungFuLootBox: kungFu,
  spongeLootBox: sponge,
  shrekLootBox: shrek,
  justiceLootBox: justice,
  scoobyLootBox: scooby,
  combatLootBox: combat,
  giftLootBox: gift,
  madagascarLootBox: madagascar,
  hustleLootBox: hustle,
  throneLootBox: throne,
  sleepaLootBox: sleepa
}

export const LOOTBOX_TYPE_TO_STYLE: Record<LootBoxType, 'blue' | 'gold' | 'pink'> = {
  rainbowLootBox: 'gold',
  luckyLootBox: 'pink',
  cryptoLootBox: 'blue',
  moonLootBox: 'blue',
  easterLootBox: 'pink',
  minecraftLootBox: 'gold',
  bananaLootBox: 'blue',
  starWarsLootBox: 'blue',
  infinityLootBox: 'blue',
  kungFuLootBox: 'blue',
  spongeLootBox: 'blue',
  shrekLootBox: 'blue',
  justiceLootBox: 'blue',
  scoobyLootBox: 'blue',
  combatLootBox: 'gold',
  giftLootBox: 'pink',
  madagascarLootBox: 'gold',
  hustleLootBox: 'gold',
  throneLootBox: 'gold',
  sleepaLootBox: 'gold'
}

export const LOOTBOX_TYPE_TO_EXPLOSION_IMAGE: Record<LootBoxType, string> = {
  rainbowLootBox: rainbowExplosion,
  luckyLootBox: luckyExplosion,
  cryptoLootBox: luckyExplosion,
  moonLootBox: rainbowExplosion,
  easterLootBox: rainbowExplosion,
  minecraftLootBox: luckyExplosion,
  bananaLootBox: luckyExplosion,
  starWarsLootBox: luckyExplosion,
  infinityLootBox: rainbowExplosion,
  kungFuLootBox: luckyExplosion,
  spongeLootBox: rainbowExplosion,
  shrekLootBox: luckyExplosion,
  justiceLootBox: rainbowExplosion,
  scoobyLootBox: rainbowExplosion,
  combatLootBox: luckyExplosion,
  giftLootBox: rainbowExplosion,
  madagascarLootBox: luckyExplosion,
  hustleLootBox: luckyExplosion,
  throneLootBox: luckyExplosion,
  sleepaLootBox: luckyExplosion
}

export const LOOTBOX_TYPE_TO_SHINE_IMAGE: Record<LootBoxType, string> = {
  rainbowLootBox: shinePink,
  luckyLootBox: shineGold,
  cryptoLootBox: shineBlue,
  moonLootBox: shineBlue,
  easterLootBox: shineGold,
  minecraftLootBox: shineGold,
  bananaLootBox: shineBlue,
  starWarsLootBox: shineGold,
  infinityLootBox: shinePink,
  kungFuLootBox: shineGold,
  spongeLootBox: shinePink,
  shrekLootBox: shineGold,
  justiceLootBox: shineBlue,
  scoobyLootBox: shineBlue,
  combatLootBox: shineGold,
  giftLootBox: shineBlue,
  madagascarLootBox: shineGold,
  hustleLootBox: shineGold,
  throneLootBox: shineGold,
  sleepaLootBox: shineGold
}

export type LootboxRewardId =
  | 2132
  | 2131
  | 2130
  | 2126
  | 2125
  | 2124
  | 2120
  | 2119
  | 2118
  | 2114
  | 2113
  | 2112
  | 2108
  | 2107
  | 2106
  | 2102
  | 2101
  | 2100
  | 2099
  | 2098
  | 2097
  | 2096
  | 2094
  | 2093
  | 2092
  | 2091
  | 2090
  | 2089
  | 2087
  | 2086
  | 2085
  | 2084
  | 2083
  | 2082
  | 2080
  | 2079
  | 2078
  | 2077
  | 2076
  | 2075
  | 276
  | 275
  | 274
  | 273
  | 272
  | 271
  | 270
  | 269
  | 268
  | 267
  | 266
  | 265
  | 263
  | 264
  | 259
  | 261
  | 260
  | 262
  | 258
  | 257
  | 256
  | 255
  | 254
  | 253
  | 251
  | 250
  | 249
  | 248
  | 247
  | 246
  | 244
  | 243
  | 242
  | 240
  | 241
  | 244
  | 238
  | 237
  | 236
  | 235
  | 234
  | 233
  | 232
  | 231
  | 230
  | 229
  | 9
  | 8
  | 7
  | 228
  | 227
  | 226
  | 225
  | 224
  | 223
  | 222
  | 221
  | 220
  | 210
  | 200
  | 190
  | 180
  | 170
  | 6
  | 5
  | 4
  | 600
  | 503
  | 506
  | 502
  | 500
  | 2
  | 505
  | 501
  | 504
  | 3
  | 1
  | 0

export const LOOTBOX_REWARDS_ORDER: Array<LootboxRewardId> = [
  2132, 2131, 2130, 2126, 2125, 2124, 2120, 2119, 2118, 2114, 2113, 2112, 2108, 2107, 2106, 2102,
  2101, 2100, 2099, 2098, 2097, 2096, 2094, 2093, 2092, 2091, 2090, 2089, 2087, 2086, 2085, 2084,
  2083, 2082, 2080, 2079, 2078, 2077, 2076, 2075, 276, 275, 274, 273, 272, 271, 270, 269, 268, 267,
  266, 265, 263, 264, 259, 261, 260, 262, 258, 257, 256, 255, 254, 253, 251, 250, 249, 248, 247,
  246, 244, 243, 242, 240, 241, 244, 238, 237, 236, 235, 234, 233, 232, 231, 230, 229, 9, 8, 7, 228,
  227, 226, 225, 224, 223, 222, 221, 220, 210, 200, 190, 180, 170, 6, 5, 4, 600, 503, 506, 502, 500,
  2, 505, 501, 504, 3, 1, 0
]

export const LOOTBOX_REWARD_ID_TO_IMAGE: Record<LootboxRewardId, string> = {
  0: soft,
  1: unlimitedLives,
  2: magnet,
  3: hard,
  4: skin2000,
  5: skin2001,
  6: skin2002,
  7: skin2003,
  8: skin2004,
  9: skin2005,
  170: skin2006,
  180: skin2007,
  190: skin2008,
  200: skin2009,
  210: skin2010,
  220: skin2011,
  221: skin2015,
  222: skin2016,
  223: skin2017,
  224: skin2018,
  225: skin2019,
  226: skin2020,
  227: skin2021,
  228: skin2022,
  229: skin2023,
  230: skin2024,
  231: skin2025,
  232: skin2026,
  233: skin2027,
  234: skin2028,
  235: skin2029,
  236: skin2030,
  237: skin2031,
  238: skin2032,
  240: skin2034,
  241: skin2035,
  242: skin2036,
  243: skin2037,
  244: skin2038,
  246: skin2040,
  247: skin2041,
  248: skin2042,
  249: skin2043,
  250: skin2044,
  251: skin2045,
  253: skin2047,
  254: skin2048,
  255: skin2049,
  256: skin2050,
  257: skin2051,
  258: skin2052,
  259: skin2053,
  260: skin2054,
  261: skin2055,
  262: skin2056,
  263: skin2057,
  264: skin2058,
  265: skin2061,
  266: skin2064,
  267: skin2062,
  268: skin2065,
  269: skin2063,
  270: skin2066,
  271: skin2068,
  272: skin2069,
  273: skin2070,
  274: skin2071,
  275: skin2072,
  276: skin2073,
  2075: skin2075,
  2076: skin2076,
  2077: skin2077,
  2078: skin2078,
  2079: skin2079,
  2080: skin2080,
  2082: skin2082,
  2083: skin2083,
  2084: skin2084,
  2085: skin2085,
  2086: skin2086,
  2087: skin2087,
  2089: skin2089,
  2090: skin2090,
  2091: skin2091,
  2092: skin2092,
  2093: skin2093,
  2094: skin2094,
  2096: skin2096,
  2097: skin2097,
  2098: skin2098,
  2099: skin2099,
  2100: skin2100,
  2101: skin2101,
  2102: skin2102,
  2106: skin2106,
  2107: skin2107,
  2108: skin2108,
  2112: skin2112,
  2113: skin2113,
  2114: skin2114,
  2118: skin2118,
  2119: skin2119,
  2120: skin2120,
  2124: skin2124,
  2125: skin2125,
  2126: skin2126,
  2130: skin2130,
  2131: skin2131,
  2132: skin2132,
  500: magnet,
  501: jumper,
  502: aim,
  503: horn,
  504: tickets,
  505: jumper,
  506: aim,
  600: wheelSpin
}

export const LOOTBOX_REWARD_ID_TO_TYPE: Record<LootboxRewardId, RewardType> = {
  0: 'soft',
  1: 'unlimitedLives',
  2: 'timeBoundMagneticField',
  3: 'hard',
  4: 'skin',
  5: 'skin',
  6: 'skin',
  7: 'skin',
  8: 'skin',
  9: 'skin',
  170: 'skin',
  180: 'skin',
  190: 'skin',
  200: 'skin',
  210: 'skin',
  220: 'skin',
  221: 'skin',
  222: 'skin',
  223: 'skin',
  224: 'skin',
  225: 'skin',
  226: 'skin',
  227: 'skin',
  228: 'skin',
  229: 'skin',
  230: 'skin',
  231: 'skin',
  232: 'skin',
  233: 'skin',
  234: 'skin',
  235: 'skin',
  236: 'skin',
  237: 'skin',
  238: 'skin',
  240: 'skin',
  241: 'skin',
  242: 'skin',
  243: 'skin',
  244: 'skin',
  246: 'skin',
  247: 'skin',
  248: 'skin',
  249: 'skin',
  250: 'skin',
  251: 'skin',
  253: 'skin',
  254: 'skin',
  255: 'skin',
  256: 'skin',
  257: 'skin',
  258: 'skin',
  259: 'skin',
  260: 'skin',
  261: 'skin',
  262: 'skin',
  263: 'skin',
  264: 'skin',
  265: 'skin',
  266: 'skin',
  267: 'skin',
  268: 'skin',
  269: 'skin',
  270: 'skin',
  271: 'skin',
  272: 'skin',
  273: 'skin',
  274: 'skin',
  275: 'skin',
  276: 'skin',
  2075: 'skin',
  2076: 'skin',
  2077: 'skin',
  2078: 'skin',
  2079: 'skin',
  2080: 'skin',
  2082: 'skin',
  2083: 'skin',
  2084: 'skin',
  2085: 'skin',
  2086: 'skin',
  2087: 'skin',
  2089: 'skin',
  2090: 'skin',
  2091: 'skin',
  2092: 'skin',
  2093: 'skin',
  2094: 'skin',
  2096: 'skin',
  2097: 'skin',
  2098: 'skin',
  2099: 'skin',
  2100: 'skin',
  2101: 'skin',
  2102: 'skin',
  2106: 'skin',
  2107: 'skin',
  2108: 'skin',
  2112: 'skin',
  2113: 'skin',
  2114: 'skin',
  2118: 'skin',
  2119: 'skin',
  2120: 'skin',
  2124: 'skin',
  2125: 'skin',
  2126: 'skin',
  2130: 'skin',
  2131: 'skin',
  2132: 'skin',
  500: 'stackableMagneticField',
  501: 'stackableJumper',
  502: 'stackableAimbot',
  503: 'magicHorns',
  504: 'tickets',
  505: 'timeBoundJumper',
  506: 'timeBoundAimbot',
  600: 'wheelSpins'
}

export const LOOTBOX_REWARD_ID_TO_SKIN_ID: Record<number, number> = {
  4: 2000,
  5: 2001,
  6: 2002,
  7: 2003,
  8: 2004,
  9: 2005,
  170: 2006,
  180: 2007,
  190: 2008,
  200: 2009,
  210: 2010,
  220: 2011,
  221: 2015,
  222: 2016,
  223: 2017,
  224: 2018,
  225: 2019,
  226: 2020,
  227: 2021,
  228: 2022,
  229: 2023,
  230: 2024,
  231: 2025,
  232: 2026,
  233: 2027,
  234: 2028,
  235: 2029,
  236: 2030,
  237: 2031,
  238: 2032,
  240: 2034,
  241: 2035,
  242: 2036,
  243: 2037,
  244: 2038,
  246: 2040,
  247: 2041,
  248: 2042,
  249: 2043,
  250: 2044,
  251: 2045,
  253: 2047,
  254: 2048,
  255: 2049,
  256: 2050,
  257: 2051,
  258: 2052,
  259: 2053,
  260: 2054,
  261: 2055,
  262: 2056,
  263: 2057,
  264: 2058,
  265: 2061,
  266: 2064,
  267: 2062,
  268: 2065,
  269: 2063,
  270: 2066,
  271: 2068,
  272: 2069,
  273: 2070,
  274: 2071,
  275: 2072,
  276: 2073,
  2075: 2075,
  2076: 2076,
  2077: 2077,
  2078: 2078,
  2079: 2079,
  2080: 2080,
  2082: 2082,
  2083: 2083,
  2084: 2084,
  2085: 2085,
  2086: 2086,
  2087: 2087,
  2088: 2088,
  2089: 2089,
  2090: 2090,
  2091: 2091,
  2092: 2092,
  2093: 2093,
  2094: 2094,
  2095: 2095,
  2096: 2096,
  2097: 2097,
  2098: 2098,
  2099: 2099,
  2100: 2100,
  2101: 2101,
  2102: 2102,
  2106: 2106,
  2107: 2107,
  2108: 2108,
  2112: 2112,
  2113: 2113,
  2114: 2114,
  2118: 2118,
  2119: 2119,
  2120: 2120,
  2124: 2124,
  2125: 2125,
  2126: 2126,
  2130: 2130,
  2131: 2131,
  2132: 2132
}

export const RAINBOW_LOOTBOX_REWARD_ID_TO_CHANCE: Record<number, number> = {
  226: 1, // Uni Genie
  225: 1, // Uni Mixture
  224: 2, // Uni Fairy
  223: 2, // Uni Cloud
  222: 2, // Uni Princess
  221: 3, // Uni Mushroom
  6: 3, // Uni Beat
  5: 3, // Mystic Uni
  4: 3, // Chill Uni
  600: 2, // WheelSpins
  502: 6, // Aimbot
  500: 6, // Magnetic Field
  501: 6, // Jumper
  504: 60 // Ticket
}

export const LUCKY_LOOTBOX_REWARD_ID_TO_CHANCE: Record<number, number> = {
  232: 1, // Uni Dice
  231: 1, // Uni Rabbit
  230: 1, // Uni Neko
  229: 1, // Uni Rich
  9: 2, // Uni Inferno
  8: 2, // Uni Reaper
  7: 2, // Uni Relic
  228: 2, // Uni Gift
  227: 3, // Uni Rubik
  600: 10, // WheelSpins
  503: 10, // MagicHorn
  502: 10, // Aimbot
  500: 10, // MagneticField
  501: 10, // Jumper
  0: 35 // Soft
}

export const LIMITED_BOX_REWARD_ID_TO_CHANCE: Record<number, number> = {
  2132: 1,
  2131: 3,
  2130: 6,
  600: 10, // WheelSpins
  503: 10, // MagicHorn
  502: 15, // Aimbot
  500: 15, // MagneticField
  501: 15, // Jumper
  0: 23 // Soft
}
