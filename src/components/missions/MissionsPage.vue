<script setup lang="ts">
import { computed, ref, useTemplateRef, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import MissionItem from '@/components/missions/MissionItem.vue';
import MissionDetails from '@/components/missions/MissionDetails.vue';
import LoaderText from '@/components/LoaderText.vue';

import {
  MAIN_CHANNEL_LINK,
  COMMUNITY_CHAT_LINK,
  X_LINK,
  BOOST_LINK,
} from '@/constants'
import { openTelegramLink, openLink } from '@telegram-apps/sdk'
import { useGlobalTasks } from '@/services/client/useTasks';
import { useDailyTasks } from '@/services/client/useTasks';
import {
  useClaimGlobalTask,
  useClaimDailyTask,
  useClaimTaskLootbox,
} from '@/services/client/useClaimTask';
import { useCheckGlobalTask } from '@/services/client/useCheckTask';
import { useCompleteUnverifiedTask } from '@/services/client/useCompleteUnverifiedTask';
import { useReferralLink } from '@/composables/useReferralLink'
import { useWalletConnection } from '@/composables/useWallet'
import { toNano, useTonTransaction } from '@/composables/useTonTransaction';
import {
  type MissionName,
  MISSIONS_IMAGES,
  MISSIONS_ORDER,
  PARTNERS_MISSIONS
} from '@/constants/missions'
import type { DailyTask, Task, RewardInfo } from '@/services/openapi';
import CountdownTimerManual from '@/components/UI/CountdownTimerManual.vue';
import { addToHomeScreen } from '@telegram-apps/sdk'
import { useToast } from '@/stores/toastStore';
import { useHasUserSeenTasks } from '@/stores/hasUserSeenStore';
import { useTonWallet } from '@/composables/useLocalWallet';
import { useDailyTasksTimer } from '@/composables/useDailyTasksTimer.ts'
import { useReward } from '@/composables/useReward';
import rainbowBox from '@/assets/images/temp/big-icons/rainbowBox.png'
import { useUserStore } from '@/stores/userStore';
import { type MissionsScrollTarget } from '@/types'

import { checkPockergamSubscription } from '@/services/partners/pokergram';
import { checkBoinkers10Spins, checkBoinkers3MoonedBoinkers } from '@/services/partners/boinkers';
import { checkMemhustlePurchaseFort, checkMemhustleTutorialComplete } from '@/services/partners/memhustle';
import { cloudStorageService } from '@/shared/storage/cloudStorageService';
import { useNowTimestamp } from '@/services/client/useNowTimestamp';
import { getMobileOS } from '@/utils/os';

const UNVERIFIED_TASK_PREFIX = 'unverified-task_'
const MIN_TIME_TO_COMPLETE_TASK = __DEV__
  ? 5 // 5 seconds
  : 60 * 5 // 5 minutes

const { t } = useI18n()
const router = useRouter()
const route = useRoute()

const { showToast } = useToast()
const { showReward } = useReward()
const userStore = useUserStore()
const { getNow } = useNowTimestamp()

const { checkGlobalTask } = useCheckGlobalTask()
const { claimGlobalTask } = useClaimGlobalTask()
const { claimDailyTask } = useClaimDailyTask()
const { claimDailyLootbox } = useClaimTaskLootbox()
const { completeUnverifiedTask } = useCompleteUnverifiedTask()
const isOpeningLootbox = ref(false)

const {
  isLoading: isLoadingGlobalTasks,
  globalTasks,
  refetchTasks: refetchGlobalTasks
} = useGlobalTasks()

const {
  isLoading: isLoadingDailyTasks,
  currentDailyTasks,
  currentOnboardingTasks,
  isLootboxAvailable,
  lootboxProgress,
} = useDailyTasks()

const { hours, minutes, seconds } = useDailyTasksTimer();

const isLoadingTasks = computed(() => isLoadingGlobalTasks.value || isLoadingDailyTasks.value)

const { forwardRefLink } = useReferralLink()
const { wallet } = useTonWallet()
const { connectWallet } = useWalletConnection(refetchGlobalTasks)
const { sendTransaction } = useTonTransaction()

// use for crosstasks that have ?startapp= parameter
const miniAppTask = (link: string) => (id: number) => {
  openTelegramLink(link)
  setTimeout(async () => {
    await completeUnverifiedTask(id)
  }, 10000)
}

// use for crosstasks that have ?start= parameter
const botTask = (link: string) => (id: number) => {
  completeUnverifiedTask(id).then(() => {
    openTelegramLink(link)
  })
}

const checkPartnerTask = (checker: (id: number) => Promise<boolean>) => (id: number, feedback = true) => {
  checker(id).then((isDone) => {
    if (isDone) {
      completeUnverifiedTask(id)
    } else if (feedback) {
      showToast(t('earn.error.partnerTaskNotCompleted'), 'warning', 5000)
    }
  })
}

const storeUnverifiedTaskTimestamp = async (id: number) => {
  const timestamp = await cloudStorageService.load<number>(UNVERIFIED_TASK_PREFIX + id) ?? 0
  if (!timestamp) {
    cloudStorageService.save(UNVERIFIED_TASK_PREFIX + id, await getNow())
  }
}

const MISSION_ACTIONS: Record<MissionName, (id: number) => void> = {
  subscribe_main_channel: () => openTelegramLink(MAIN_CHANNEL_LINK),
  invite_1_friend: () => forwardRefLink(),
  invite_3_friend: () => forwardRefLink(),
  invite_5_friend: () => forwardRefLink(),
  connect_wallet: () => connectWallet(),
  first_transaction: () => wallet.value !== null ? sendTransaction(toNano(0.5)) : connectWallet(),
  invite_ref: () => forwardRefLink(),
  use_booster: () => router.push({ name: 'home' }),
  jump_to_score: () => router.push({ name: 'home' }),
  use_jumper_booster: () => router.push({ name: 'home' }),
  use_magnet_booster: () => router.push({ name: 'home' }),
  use_aimbot_booster: () => router.push({ name: 'home' }),
  kill_monster: () => router.push({ name: 'home' }),
  play_game: () => router.push({ name: 'home' }),
  catch_ticket: () => router.push({ name: 'home' }),
  unlock_league: () => router.push({ name: 'home' }),
  daily_total_jump: () => router.push({ name: 'home' }),
  use_revive: () => router.push({ name: 'home' }),
  buy_skin: () => router.push({ name: 'me' }),
  subscribe_community_chat: () => openTelegramLink(COMMUNITY_CHAT_LINK),
  subscribe_x: async (id: number) => {
    openLink(X_LINK)
    await completeUnverifiedTask(id)
  },
  add_to_home_screen: () => {
    if (addToHomeScreen.isAvailable()) {
      addToHomeScreen();
    } else {
      showToast(t('errors.appVersion'), 'warning', 5000)
    }
  },
  purchase_in_shop_for_stars: () => router.push({ name: 'shop' }),
  purchase_skin_for_stars: () => router.push('/menu/me'),
  boost_telegram_channel:() => openTelegramLink(BOOST_LINK),
  go_to_miniapp_9: miniAppTask('https://t.me/MatchMoney_gamebot/start?startapp=userId%3D7457809942%3D%3Dutm_source%3Dtma%3D%3Dutm_campaign%3Dcampaign_1_unijumpbot%3D%3Dutm_partner%3Dunijumpbot'),
  go_to_miniapp_10: miniAppTask('https://t.me/boinker_bot/boinkapp?startapp=boink7457809942campUniJump'),
  go_to_miniapp_15: miniAppTask('https://t.me/BitGame_Online_bot/MINER_Play2Earn?startapp=adj_share_invite_AgpUPXUnhWZKBfHN'),
  go_to_miniapp_19: miniAppTask('https://t.me/diggerton_bot/dig?startapp=bro7457809942'),
  go_to_miniapp_22: miniAppTask('https://t.me/drft_party_bot/game?startapp=unijump'),
  go_to_miniapp_23: miniAppTask('https://t.me/brickwalls_bot/brickapp?startapp=u5ML5_partnerx&startApp=u5ML5_partnerx'),
  go_to_miniapp_24: miniAppTask('https://t.me/OutmineBot/outmine?startapp=unijump'),
  go_to_miniapp_25: miniAppTask('https://t.me/duckygram_bot/play?startapp=89d74db1-e6bb-4018-b58c-9363a1579704'),
  go_to_miniapp_26: miniAppTask('https://t.me/symptomify_bot/app?startapp=7457809942'),
  go_to_miniapp_27: miniAppTask('https://t.me/DOGS03_bot/app?startapp=copy_7457809942'),
  go_to_miniapp_28: miniAppTask('https://t.me/hash_cats_bot/app?startapp=RSX70vjABY'),
  go_to_miniapp_29: miniAppTask('https://t.me/RichDogGameBot/Play?startapp=kentId7457809942'),
  go_to_miniapp_30: miniAppTask('https://t.me/Simple_Tap_Bot/app?startapp=1741705536807'),
  go_to_miniapp_31: miniAppTask('https://t.me/PookieChase_bot?startapp=5995224794'),
  go_to_miniapp_32: botTask('https://t.me/TowerTon_bot?start=7457809942'),
  go_to_miniapp_33: botTask('https://t.me/fasqonbot?start=7457809942'),
  go_to_miniapp_34: miniAppTask('https://t.me/the_capybara_meme_bot/start?startapp=db75b50ea351f321ee370df61c5000ef'),
  go_to_miniapp_35: miniAppTask('https://t.me/spaceadv_game_bot/play?startapp=7457809942'),
  go_to_miniapp_36: miniAppTask('https://t.me/StarsEarner_bot/app?startapp=7457809942'),
  go_to_miniapp_37: botTask('https://t.me/+3z1RUvc0rpplNDBl'),
  go_to_miniapp_38: botTask('https://t.me/justfab_bot?start=7457809942'),
  go_to_miniapp_39: miniAppTask('https://t.me/tapxbattle_bot/play?startapp=529b4f63-3180-49cc-b97b-33b8b78fb985'),
  go_to_miniapp_40: miniAppTask('https://t.me/pixiland_bot/play?startapp=QA0LTWM0L4_GAMEUNIJUMP'),
  go_to_miniapp_41: miniAppTask('https://t.me/biztycoon_bot/?startapp=game_unijump'),
  go_to_miniapp_42: miniAppTask('https://t.me/appss/store?startapp=b2ZmZXJzLTU1JDY1'),
  go_to_miniapp_43: miniAppTask('https://t.me/puparty_bot/index?startapp=10825228'),
  go_to_miniapp_44: miniAppTask('https://t.me/gumart_bot/join?startapp=7457809942'),
  go_to_miniapp_45: miniAppTask('https://t.me/PokergramComBot/pokerapp?startapp=campUniJump'),
  go_to_miniapp_46: miniAppTask('https://t.me/Agent301Bot/app?startapp=onetime7457809942'),
  go_to_miniapp_47: miniAppTask('https://t.me/EmptyPepeBot/app?startapp=ref_cIMsRR'),
  go_to_miniapp_48: miniAppTask('https://t.me/GenkiMinerBot/GenkiMiner?startapp=S2osjYhL'),
  go_to_miniapp_49: miniAppTask('https://t.me/beetz_bot/play?startapp=ref_IpWwqY9pCFK'),
  go_to_miniapp_50: miniAppTask('https://t.me/pepecase_bot/cases?startapp=ref_6057805700'),
  go_to_miniapp_51: miniAppTask('https://t.me/RewardsHQ_bot/RewardsHQ?startapp=7457809942'),
  subscribe_mem_hustle_channel: () => openTelegramLink('https://t.me/+f05rS8oQ0xk3MjE6'),
  subscribe_ton_station_channel: () => openTelegramLink('https://t.me/+gaEYc-qTiqMyMjAy'),

  // Boinkers
  partners_task_1: () => openTelegramLink('https://t.me/boinker_bot/boinkapp?startapp=campUniJumpEvent'),
  partners_task_2: () => openTelegramLink('https://t.me/boinker_bot/boinkapp?startapp=campUniJumpEvent'),
  // Pokergram
  partners_task_3: () => openTelegramLink('https://t.me/PokergramComBot/pokerapp?startapp=campUnijumpJune'),
  // MemHustle
  partners_task_4: () => openTelegramLink('https://t.me/MemHustle_bot/memHustle?startapp=db75b50ea3'),
  partners_task_5: () => openTelegramLink('https://t.me/MemHustle_bot/memHustle?startapp=db75b50ea3'),
  // TON Station
  partners_task_6: () => openTelegramLink(import.meta.env.VITE_TON_STATION_BOT_LINK),
  partners_task_7: () => openTelegramLink(import.meta.env.VITE_TON_STATION_BOT_LINK),
  // Sleepagotchi
  partners_task_sleepagotchi_1: async (id: number) => {
    await storeUnverifiedTaskTimestamp(id)
    openTelegramLink('https://t.me/sleepagotchiLITE_bot/game?startapp=72633a37343537383039393432')
  },
  partners_task_sleepagotchi_2: async (id: number) => {
    await storeUnverifiedTaskTimestamp(id)
    const os = getMobileOS()
    if (os === 'ios') {
      openLink('https://apps.apple.com/us/app/sleepagotchi/id6743857605')
    } else {
      openLink('https://play.google.com/store/apps/details?id=com.sleepagotchi.soft.app&pli=1')
    }
  },
  // Match Money
  partners_task_match_money_1: () => openTelegramLink('https://t.me/MatchMoney_gamebot/start?startapp=userId%3D7329415767%3D%3Dutm_source%3Dtma%3D%3Dutm_campaign%3Dcampaign_2_unijump%3D%3Dutm_partner%3Dunijump'),
  partners_task_match_money_2: () => openTelegramLink('https://t.me/MatchMoney_gamebot/start?startapp=userId%3D7329415767%3D%3Dutm_source%3Dtma%3D%3Dutm_campaign%3Dcampaign_2_unijump%3D%3Dutm_partner%3Dunijump'),
  partners_task_match_money_3: () => {
    const lang = userStore.getLanguageCode()
    if (lang === 'ru') {
      openTelegramLink('https://t.me/+xJGnVVK5Ecc1OWMy')
    } else {
      openTelegramLink('https://t.me/+ZvSWtk4MaJg3MWZi')
    }
  },
}

const MISSION_CHECKS: Partial<Record<MissionName, (id: number, feedback?: boolean) => void>> = {
  partners_task_1: checkPartnerTask(checkBoinkers10Spins.bind(null, userStore.getId().toString())),
  partners_task_2: checkPartnerTask(checkBoinkers3MoonedBoinkers.bind(null, userStore.getId().toString())),
  partners_task_3: checkPartnerTask(checkPockergamSubscription.bind(null, userStore.getId().toString())),
  partners_task_4: checkPartnerTask(checkMemhustleTutorialComplete.bind(null, userStore.getId().toString())),
  partners_task_5: checkPartnerTask(checkMemhustlePurchaseFort.bind(null, userStore.getId().toString())),
  partners_task_6: checkPartnerTask(checkGlobalTask),
  partners_task_7: checkPartnerTask(checkGlobalTask),
  partners_task_match_money_1: checkPartnerTask(checkGlobalTask),
  partners_task_match_money_2: checkPartnerTask(checkGlobalTask),
  partners_task_match_money_3: checkPartnerTask(checkGlobalTask),
}

const MISSIONS_TO_CHECK = Object.keys(MISSION_CHECKS)

const autoCheckPartnerTask = async () => {
  const keys = Object.keys(MISSION_CHECKS)
  for (const key of keys) {
    const task = globalTasks.value.find(m => m.name === key)
    const checker = MISSION_CHECKS[key as MissionName]
    if (task && !task.completed && checker) {
      await checker(task.taskId, false)
    }
  }
}

const checkUnverifiedTasks = async () => {
  const now = await getNow()
  const unverifiedTaskKeys = (await cloudStorageService.getKeys()).filter(key => key.startsWith(UNVERIFIED_TASK_PREFIX))
  for (const taskKey of unverifiedTaskKeys) {
    const taskId = taskKey.split('_')[1]
    const taskTime = await cloudStorageService.load<number>(taskKey) ?? 0
    const timePassed = now - taskTime
    console.log(taskKey, now, taskTime, timePassed, MIN_TIME_TO_COMPLETE_TASK)
    if (timePassed > MIN_TIME_TO_COMPLETE_TASK) {
      completeUnverifiedTask(+taskId)
      cloudStorageService.delete(taskKey)
    }
  }
}

watch([isLoadingGlobalTasks, () => globalTasks.value.length], ([loading, tasksLength]) => {
  if (!loading && tasksLength) {
    autoCheckPartnerTask()
    checkUnverifiedTasks()
  }
}, { immediate: true })

const sortedMissions = computed(() => {
  return globalTasks.value
    .slice()
    .filter(m => !m.claimed && !PARTNERS_MISSIONS.has(m.name as MissionName))
    .sort((a, b) => {
      return (
        MISSIONS_ORDER.indexOf(a.name as MissionName) -
        MISSIONS_ORDER.indexOf(b.name as MissionName)
      )
    })
})

const partnerMissions = computed(() => {
  return globalTasks.value
    .slice()
    .filter(m => !m.claimed && PARTNERS_MISSIONS.has(m.name as MissionName))
    .sort((a, b) => {
      return (
        MISSIONS_ORDER.indexOf(a.name as MissionName) -
        MISSIONS_ORDER.indexOf(b.name as MissionName)
      )
    })
})

const completedMissions = computed(() => {
  return globalTasks.value
    .slice()
    .filter(m => m.claimed)
    .sort((a, b) => {
      return (
        MISSIONS_ORDER.indexOf(a.name as MissionName) -
        MISSIONS_ORDER.indexOf(b.name as MissionName)
      )
    })
})

const isOnboardingTasks = computed(() => {
  return currentOnboardingTasks.value.length > 0 && !currentDailyTasks.value.length
})

const sortedCurrentDailyMissions = computed(() => {
  return currentDailyTasks.value.slice().sort((a, b) => {
    return MISSIONS_ORDER.indexOf(a.name as MissionName) - MISSIONS_ORDER.indexOf(b.name as MissionName)
  })
})

const dailyMissions = computed(() => {
  return isOnboardingTasks.value
    ? currentOnboardingTasks.value : sortedCurrentDailyMissions.value
})


const allMissions = computed<Array<DailyTask | Task>>(() => {
  return [...dailyMissions.value, ...sortedMissions.value, ...partnerMissions.value]
})

const isShowDetails = ref(false)
const missionToDetailsId = ref<number | undefined>()

const missionDetails = computed(() => {
  return allMissions.value.find(m => m.taskId === missionToDetailsId.value)
    || allMissions.value[0]
})

const openDetails = (id: number) => {
  const mission = allMissions.value.find(m => m.taskId === id) || null
  if (mission) {
    missionToDetailsId.value = id
    isShowDetails.value = true
  }
}

const closeDetails = () => {
  isShowDetails.value = false
}

const doAction = (id: number) => {
  const taskName = allMissions.value.find(m => m.taskId === id)?.name as MissionName
  if (taskName && MISSION_ACTIONS[taskName]) {
    MISSION_ACTIONS[taskName](id)
  }
}

const claimReward = async (id: number, isDaily: boolean = false) => {
  let rewardResponse: RewardInfo | null = null
  if (isDaily) {
    rewardResponse = (await claimDailyTask(id)).reward
  } else {
    rewardResponse = (await claimGlobalTask(id)).reward
  }
  showReward(rewardResponse, { isAlreadyOnPlayerState: true })
}

const claimLootbox = () => {
  if (!isLootboxAvailable.value) return
  isOpeningLootbox.value = true
  claimDailyLootbox(isOnboardingTasks.value).then(() => {
    showReward({ type: 'rainbowLootBox', value: 1 })
  }).finally(() => {
    isOpeningLootbox.value = false
  })
}

const hasUserSeenStore = useHasUserSeenTasks()
const markAsSeen = (name: string) => {
  hasUserSeenStore.markTaskAsSeen(name)
}

const partnersElemRef = useTemplateRef('partnersElemRef')

const SCROLL_TARGET_TO_REF = {
  partners: partnersElemRef
}

const scrollTo = (target: MissionsScrollTarget) => {
  const element = SCROLL_TARGET_TO_REF[target]
  element.value?.scrollIntoView({ behavior: 'smooth', block: 'start' })
}
const scrollTarget = route.query.scrollTo as undefined | MissionsScrollTarget

watch(
  [isLoadingTasks, partnersElemRef],
  ([loading, el1]) => {
    if (scrollTarget && !loading && el1) {
      // TODO: all elements should be rendered for propper scrolling
      // TODO: find way without setTimeout.
      // Currently, without timeout it scrolls to not desired position
      setTimeout(() => {
        scrollTo(scrollTarget)
      }, 200)
    }
  },
  { immediate: true }
)
</script>

<template>
  <div class="missions-page" v-bind="$attrs">
    <header class="text-center space-y-[2px] pb-[31px] tracking-normal">
      <h1 class="text-[30px] leading-[40px] text-shadow">
        {{ t('earn.title') }}
      </h1>
      <p class="font-extrabold text-[15px] leading-[22px] text-[#6DB0ED]">
        {{ t('earn.description') }}
      </p>
    </header>
    <div v-if="!isLoadingTasks" class="mb-4">
      <div class="flex justify-start gap-x-2">
        <p class="text-[16px] leading-[22px] mb-1 text-shadow text-shadow_black">
          {{ isOnboardingTasks ? t('earn.startTasks') : t('earn.dailyTasks') }}
        </p>
        <div
          v-if="!isOnboardingTasks"
          class="h-[20px] px-[3px] flex items-center rounded-[5px] bg-[#07070773]"
        >
          <CountdownTimerManual
            class="text-[12px] leading-[20px] tracking-[0.5px]"
            :hours="hours"
            :minutes="minutes"
            :seconds="seconds"
          />
        </div>
      </div>
      <div class="daily-tasks__container space-y-2">
        <div class="overflow-visible relative" @click="claimLootbox">
          <div class="daily-tasks__lootbox">
            <p class="text-[15px] leading-[20px] text-white text-shadow text-shadow_black mb-1">
              {{t('earn.tasksCompleted')}} {{ lootboxProgress }}/{{ dailyMissions.length }}
            </p>
            <div class="daily-tasks__lootbox-progress">
              <div
                v-for="_, index in dailyMissions"
                :key="index"
                class="daily-tasks__lootbox-progress-step"
                :class="{
                  'daily-tasks__lootbox-progress-step_active': index < lootboxProgress
                }"
              ></div>
            </div>
          </div>
          <img
            class="absolute bottom-[4px] right-[19px] w-[100px]"
            :class="{
              'daily-tasks__lootbox_pulse': isLootboxAvailable,
            }"
            :src="rainbowBox"
            alt="rainbow lootbox"
          />
          <div v-if="!isLootboxAvailable && lootboxProgress >= dailyMissions.length" class="absolute right-[32px] bottom-[12px] text-[16px] text-shadow text-shadow_black">
            {{t('claimed')}}
          </div>
        </div>
        <MissionItem
          v-for="item in dailyMissions"
          :key="item.taskId"
          :id="item.taskId"
          :image="MISSIONS_IMAGES[item.name as MissionName] ?? ''"
          :description="item.name"
          :reward="item.reward"
          :is-done="item.completed"
          :is-collected="item.claimed"
          :progress="{
            current: item.current,
            goal: item.target
          }"
          @click="openDetails"
        />
      </div>
    </div>
    <div v-if="!isLoadingTasks && sortedMissions.length" class="mb-4">
      <p class="text-[16px] leading-[22px] mb-1 text-shadow text-shadow_black">
        {{ t('earn.missions.title') }}
      </p>
      <div class="space-y-2">
        <template
          v-for="item in sortedMissions"
          :key="item.taskId"
        >
          <MissionItem
            v-if="MISSIONS_IMAGES[item.name as MissionName]"
            :id="item.taskId"
            :image="MISSIONS_IMAGES[item.name as MissionName] ?? ''"
            :description="item.name"
            :reward="item.reward"
            :is-done="item.completed"
            :is-collected="item.claimed"
            :is-new="hasUserSeenStore.newTasks.includes(item.name)"
            @click="(id) => (
              openDetails(id),
              hasUserSeenStore.newTasks.includes(item.name) && markAsSeen(item.name)
            )"
          />
        </template>
      </div>
    </div>
    <div ref="partnersElemRef" v-if="!isLoadingTasks && partnerMissions.length" class="mb-4">
      <p class="text-[16px] leading-[22px] mb-1 text-shadow text-shadow_black">
        {{ t('earn.missions.partners') }}
      </p>
      <div class="space-y-2">
        <template
          v-for="item in partnerMissions"
          :key="item.taskId"
        >
          <MissionItem
            v-if="MISSIONS_IMAGES[item.name as MissionName]"
            :id="item.taskId"
            :image="MISSIONS_IMAGES[item.name as MissionName] ?? ''"
            :description="item.name"
            :reward="item.reward"
            :is-done="item.completed"
            :is-collected="item.claimed"
            :is-new="hasUserSeenStore.newTasks.includes(item.name)"
            @click="(id) => (
              openDetails(id),
              hasUserSeenStore.newTasks.includes(item.name) && markAsSeen(item.name)
            )"
          />
        </template>
      </div>
    </div>
    <div v-if="!isLoadingTasks && completedMissions.length">
      <p class="text-[16px] leading-[22px] mb-1 text-shadow text-shadow_black">
        {{ t('earn.completedMissions') }}
      </p>
      <div class="space-y-2">
        <template v-for="item in completedMissions" :key="item.taskId">
          <MissionItem
            v-if="MISSIONS_IMAGES[item.name as MissionName]"
            :id="item.taskId"
            :image="MISSIONS_IMAGES[item.name as MissionName] ?? ''"
            :description="item.name"
            :reward="item.reward"
            :is-done="item.completed"
            :is-collected="item.claimed"
            :is-new="hasUserSeenStore.newTasks.includes(item.name)"
            @click="(id) => (
              openDetails(id),
              hasUserSeenStore.newTasks.includes(item.name) && markAsSeen(item.name)
            )"
          />
        </template>
      </div>
    </div>
    <LoaderText class="text-[#AFD0FF] text-[20px] text-center mt-11" :isLoading="isLoadingTasks" />
    <div class="absolute top-0 left-0 w-full h-full bg-[#00000062] flex items-center justify-center z-10" v-if="isOpeningLootbox">
      <LoaderText class="text-[#AFD0FF] text-[20px] text-center" is-loading />
    </div>
  </div>
  <MissionDetails
    v-if="!isLoadingTasks"
    :isOpen="isShowDetails"
    :id="missionDetails.taskId"
    :image="MISSIONS_IMAGES[missionDetails.name as MissionName] ?? ''"
    :description="missionDetails.name"
    :reward="missionDetails.reward"
    :is-done="missionDetails.completed"
    :is-collected="missionDetails.claimed"
    :progress="{
      current: (missionDetails as DailyTask).current ?? 0,
      goal: (missionDetails as DailyTask).target ?? 0
    }"
    :should-check="MISSIONS_TO_CHECK.includes(missionDetails.name as MissionName)"
    @do-action="doAction"
    @check="(id) => MISSION_CHECKS[missionDetails.name as MissionName]?.(id)"
    @claim-reward="(id, isDaily) => (claimReward(id, isDaily), closeDetails())"
    @close="closeDetails"
  />
</template>

<style lang="scss">
.missions-page {
  width: inherit;
  overflow: visible;
  padding: 22px 20px;
}

.daily-tasks {
  &__container {
    margin: 0 -9px;
    padding: 10px 9px;
    background-color: #003579;
    border-radius: 11px;
  }

  &__lootbox {
    position: relative;
    height: 68px;
    width: 100%;
    padding: 14px 140px 16px 30px;

    background: linear-gradient(360deg, #9C44FE 0%, #F97EFF 92.65%);
    box-shadow: #00000033 0 2px, inset #FFCFFD 0 4px, inset #9700EF 0 -5px;
    border: 1px #5E006A solid;
    border-radius: 9px;

    &-progress {
      width: 100%;
      display: flex;
      gap: 7px;

      &-step {
        flex: 1;
        height: 15px;
        background: linear-gradient(360deg, #6F1CC2 -31.76%, #56117C 56.08%);
        transform: skew(-15deg);
        border-radius: 7px 5px 7px 5px;
        padding: 2px;

        &_active {
          &::before {
            content: '';
            display: block;
            width: 100%;
            height: 100%;
            background: linear-gradient(360deg, #FFA200 -88.81%, #FFE02F 175.52%);
            border-radius: 5px 3px;
          }
        }
      }
    }

    &_pulse {
      animation: pulse 1s infinite;
    }

    @keyframes pulse {
      0% {
        transform: scale(1);
      }
      50% {
        transform: scale(1.1);
      }
      100% {
        transform: scale(1);
      }
    }
  }
}
</style>
