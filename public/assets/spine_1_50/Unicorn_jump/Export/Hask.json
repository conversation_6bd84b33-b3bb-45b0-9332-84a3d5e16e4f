{"skeleton": {"hash": "ezgrumcIgr4", "spine": "4.2.40", "x": -183.16, "y": -100.58, "width": 343, "height": 208, "images": "./Images/Monster_Hask/", "audio": "./audio"}, "bones": [{"name": "root"}, {"name": "main", "parent": "root", "y": -94.87, "icon": "square"}, {"name": "body_cntrl", "parent": "main", "rotation": 89.51, "x": 2.45, "y": 20.68, "icon": "suiteHearts"}, {"name": "body", "parent": "body_cntrl", "length": 36.56, "color": "af5d1fff"}, {"name": "legR", "parent": "body", "length": 66.01, "rotation": -167.3, "x": 48.76, "y": 133.45}, {"name": "legR_ik", "parent": "main", "x": -116.61, "y": 6.05, "color": "ff3f00ff", "icon": "ik"}, {"name": "legL", "parent": "body", "length": 66.01, "rotation": 167.83, "x": 47.47, "y": -106.69}, {"name": "legL_ik", "parent": "main", "x": 95.08, "y": 2.83, "color": "ff3f00ff", "icon": "ik"}, {"name": "face", "parent": "body", "x": 63.96, "y": -1.45, "color": "ff0000ff", "icon": "arrowsB"}, {"name": "eye", "parent": "face", "x": 9.02, "y": 0.08, "icon": "arrowUpDown"}, {"name": "brow_m", "parent": "eye", "x": 36.05, "y": -4.44, "color": "ff0000ff", "icon": "arrowsB"}, {"name": "brow", "parent": "brow_m", "x": 5.7, "y": -0.21, "icon": "diamond"}, {"name": "pupil", "parent": "body", "x": 95.4, "y": -9.84, "icon": "rotate"}, {"name": "eye2", "parent": "brow_m", "x": 20.07, "y": 40.46}, {"name": "eye3", "parent": "brow_m", "x": 12.41, "y": -33.12}, {"name": "mouth", "parent": "face", "rotation": -89.51, "x": 4.41, "y": 0.67, "color": "ffde00ff", "icon": "mouth"}, {"name": "eye_up_closed", "parent": "eye", "x": 21.66, "y": -2.32, "color": "ffb500ff"}, {"name": "death", "parent": "root", "y": 55.05, "color": "ff9000ff", "icon": "asterisk"}, {"name": "blot", "parent": "death", "rotation": -0.04, "icon": "flower"}, {"name": "blot_drops_control", "parent": "blot", "y": -10.68, "icon": "arrowDown"}, {"name": "Drops", "parent": "blot", "rotation": 0.04}, {"name": "blot_drop2", "parent": "Drops"}, {"name": "blot_drop_s1", "parent": "Drops"}, {"name": "blot_drop3", "parent": "Drops", "scaleX": 0.8698, "scaleY": 0.8698}, {"name": "blot_drop4", "parent": "Drops", "scaleX": 1.2553, "scaleY": 1.2553}, {"name": "blot_drop_s2", "parent": "Drops"}, {"name": "blot_drop5", "parent": "Drops"}, {"name": "blot_drop_s3", "parent": "Drops"}, {"name": "blot_drop6", "parent": "Drops", "scaleX": 0.8698, "scaleY": 0.8698}, {"name": "blot_drop_s4", "parent": "Drops"}], "slots": [{"name": "body_outline", "bone": "main", "attachment": "body_outline"}, {"name": "leg_r_outline", "bone": "main", "attachment": "leg_r_outline"}, {"name": "jaw_outline", "bone": "main", "attachment": "jaw_outline"}, {"name": "leg_l_outline", "bone": "main", "attachment": "leg_l_outline"}, {"name": "body", "bone": "main", "attachment": "body"}, {"name": "leg_r", "bone": "main", "attachment": "leg_r"}, {"name": "eye", "bone": "main", "attachment": "eye"}, {"name": "eye2", "bone": "main", "color": "ffffff00", "attachment": "eye_red"}, {"name": "pupil", "bone": "main", "attachment": "pupil"}, {"name": "eyelid_l", "bone": "main", "attachment": "eyelid_l"}, {"name": "eyelid_u", "bone": "main", "attachment": "eyelid_u"}, {"name": "brow_base", "bone": "main", "attachment": "brow_base"}, {"name": "brow", "bone": "main", "attachment": "brow"}, {"name": "mouth", "bone": "main", "attachment": "mouth"}, {"name": "lip_u", "bone": "main", "attachment": "lip_u"}, {"name": "jaw", "bone": "main", "attachment": "jaw"}, {"name": "leg_l", "bone": "main", "attachment": "leg_l"}, {"name": "blot", "bone": "blot", "color": "ff9520ff", "dark": "f9bb58"}, {"name": "blot_drop2", "bone": "blot_drop2", "color": "ff9520ff", "dark": "f9bb58"}, {"name": "blot_drop_s1", "bone": "blot_drop_s1", "color": "ff9520ff", "dark": "f9bb58"}, {"name": "blot_drop3", "bone": "blot_drop3", "color": "ff9520ff", "dark": "f9bb58"}, {"name": "blot_drop4", "bone": "blot_drop4", "color": "ff9520ff", "dark": "f9bb58"}, {"name": "blot_drop5", "bone": "blot_drop_s2", "color": "ff9520ff", "dark": "f9bb58"}, {"name": "blot_drop6", "bone": "blot_drop5", "color": "ff9520ff", "dark": "f9bb58"}, {"name": "blot_drop_s2", "bone": "blot_drop_s3", "color": "ff9520ff", "dark": "f9bb58"}, {"name": "blot_drop7", "bone": "blot_drop6", "color": "ff9520ff", "dark": "f9bb58"}, {"name": "blot_drop8", "bone": "blot_drop_s4", "color": "ff9520ff", "dark": "f9bb58"}], "ik": [{"name": "legL_ik", "bones": ["legL"], "target": "legL_ik", "compress": true, "stretch": true}, {"name": "legR_ik", "order": 1, "bones": ["legR"], "target": "legR_ik", "compress": true, "stretch": true}], "skins": [{"name": "default", "attachments": {"blot": {"blot": {"type": "mesh", "uvs": [0.16853, 0.04132, 0.20427, 0.04133, 0.235, 0.05776, 0.2567, 0.08503, 0.27019, 0.11893, 0.28083, 0.15284, 0.29008, 0.16859, 0.40306, 0.14175, 0.52511, 0.14243, 0.63958, 0.17341, 0.65099, 0.15069, 0.66785, 0.11583, 0.69938, 0.10078, 0.74656, 0.0982, 0.78663, 0.11447, 0.81478, 0.14412, 0.82679, 0.17844, 0.8274, 0.22268, 0.80551, 0.24979, 0.78146, 0.26764, 0.77045, 0.28539, 0.79007, 0.31899, 0.82203, 0.31695, 0.86186, 0.3347, 0.88752, 0.35974, 0.90407, 0.39632, 0.9038, 0.43154, 0.89703, 0.47361, 0.87282, 0.49811, 0.84367, 0.51769, 0.83976, 0.57695, 0.82721, 0.62819, 0.85091, 0.63399, 0.88661, 0.64029, 0.9085, 0.64943, 0.92589, 0.66799, 0.92962, 0.68768, 0.92352, 0.71185, 0.90345, 0.72913, 0.88078, 0.7345, 0.85869, 0.73204, 0.84233, 0.71683, 0.82827, 0.69353, 0.80387, 0.68283, 0.78923, 0.70054, 0.77272, 0.72456, 0.75456, 0.74543, 0.74252, 0.76337, 0.75174, 0.78241, 0.7707, 0.79541, 0.78966, 0.80098, 0.80915, 0.81654, 0.82604, 0.83018, 0.83576, 0.85319, 0.83587, 0.87721, 0.83318, 0.90725, 0.81778, 0.92725, 0.79106, 0.94395, 0.74837, 0.94704, 0.71572, 0.94051, 0.69219, 0.91664, 0.67776, 0.88946, 0.6729, 0.86403, 0.66254, 0.84197, 0.64012, 0.83666, 0.62051, 0.84647, 0.61585, 0.88202, 0.60876, 0.90761, 0.58596, 0.93801, 0.55205, 0.95614, 0.51472, 0.9587, 0.47446, 0.95391, 0.44301, 0.93005, 0.42458, 0.90092, 0.4098, 0.87756, 0.3531, 0.86126, 0.31071, 0.8462, 0.27287, 0.82849, 0.23944, 0.82863, 0.21336, 0.82462, 0.19205, 0.80713, 0.1763, 0.78103, 0.17544, 0.7546, 0.15225, 0.73275, 0.12704, 0.75153, 0.09896, 0.78407, 0.07158, 0.81002, 0.02474, 0.81038, 0.00898, 0.79541, 0.00014, 0.76864, 0.0126, 0.74387, 0.02727, 0.723, 0.06373, 0.71112, 0.1024, 0.70314, 0.11577, 0.6871, 0.11344, 0.66789, 0.10013, 0.64319, 0.07288, 0.63049, 0.0481, 0.60493, 0.0395, 0.57932, 0.04017, 0.53824, 0.05265, 0.51498, 0.04012, 0.49584, 0.02776, 0.45571, 0.02776, 0.39855, 0.042, 0.36049, 0.07071, 0.32161, 0.10651, 0.29848, 0.14993, 0.28593, 0.17204, 0.25511, 0.157, 0.23649, 0.12839, 0.21904, 0.09915, 0.19856, 0.07437, 0.1592, 0.07434, 0.10411, 0.09293, 0.06764, 0.12711, 0.04525, 0.7637, 0.87399, 0.51839, 0.89337, 0.06159, 0.75434, 0.88018, 0.68481, 0.82945, 0.40232, 0.45472, 0.47982, 0.74434, 0.1825], "triangles": [11, 12, 123, 123, 14, 15, 111, 112, 0, 77, 78, 122, 79, 122, 78, 122, 79, 80, 87, 88, 119, 116, 0, 113, 115, 116, 114, 3, 0, 2, 2, 0, 1, 117, 50, 51, 117, 52, 53, 117, 51, 52, 120, 34, 35, 120, 35, 36, 37, 120, 36, 38, 120, 37, 38, 39, 120, 120, 39, 40, 117, 58, 59, 69, 118, 68, 71, 72, 118, 70, 71, 118, 86, 119, 85, 86, 87, 119, 69, 70, 118, 57, 58, 117, 56, 57, 117, 59, 60, 117, 56, 117, 55, 40, 41, 120, 55, 117, 54, 117, 53, 54, 123, 13, 14, 116, 113, 114, 85, 119, 84, 119, 88, 89, 119, 89, 90, 3, 4, 0, 111, 0, 4, 0, 112, 113, 123, 12, 13, 95, 96, 122, 41, 42, 120, 43, 44, 31, 43, 31, 42, 42, 32, 120, 83, 94, 95, 120, 33, 34, 120, 32, 33, 45, 122, 44, 101, 96, 99, 31, 122, 30, 29, 30, 122, 122, 20, 21, 122, 121, 29, 21, 22, 121, 121, 122, 21, 29, 121, 28, 101, 108, 122, 108, 103, 107, 106, 107, 105, 101, 103, 108, 105, 107, 103, 28, 121, 27, 101, 102, 103, 105, 103, 104, 20, 9, 123, 20, 122, 9, 9, 10, 123, 10, 11, 123, 108, 109, 122, 109, 6, 122, 6, 7, 122, 122, 8, 9, 122, 7, 8, 27, 121, 26, 26, 121, 25, 121, 24, 25, 121, 23, 24, 121, 22, 23, 20, 123, 19, 19, 123, 18, 109, 110, 6, 18, 123, 17, 110, 5, 6, 110, 4, 5, 4, 110, 111, 123, 16, 17, 123, 15, 16, 31, 44, 122, 101, 99, 100, 65, 74, 122, 122, 74, 75, 64, 65, 122, 76, 122, 75, 47, 64, 122, 77, 122, 76, 122, 80, 81, 81, 82, 122, 47, 122, 46, 82, 83, 122, 46, 122, 45, 122, 83, 95, 42, 31, 32, 68, 118, 67, 72, 73, 118, 60, 61, 117, 67, 118, 66, 73, 74, 118, 66, 118, 65, 61, 62, 117, 65, 118, 74, 62, 48, 117, 117, 49, 50, 117, 48, 49, 62, 63, 48, 48, 63, 47, 63, 64, 47, 93, 119, 92, 119, 93, 84, 90, 91, 119, 119, 91, 92, 84, 93, 83, 93, 94, 83, 122, 96, 101, 96, 97, 99, 97, 98, 99], "vertices": [2, 18, -90.46, 143.76, 0.02155, 19, -90.46, 154.44, 0.97845, 2, 18, -78.96, 143.56, 0.00946, 19, -78.96, 154.24, 0.99054, 2, 18, -69.06, 140.41, 0.153, 19, -69.06, 151.08, 0.847, 2, 18, -62.08, 135.28, 0.39795, 19, -62.08, 145.96, 0.60205, 2, 18, -57.73, 128.96, 0.70584, 19, -57.73, 139.64, 0.29416, 2, 18, -54.31, 121.28, 0.93146, 19, -54.31, 131.95, 0.06854, 2, 18, -51.33, 116.34, 0.95362, 19, -51.33, 127.01, 0.04638, 2, 18, -14.95, 122.1, 0.7559, 19, -14.95, 132.78, 0.2441, 2, 18, 24.35, 121.97, 0.76179, 19, 24.35, 132.65, 0.23821, 2, 18, 61.21, 111.18, 0.74063, 19, 61.21, 121.86, 0.25937, 2, 18, 64.89, 117.25, 0.64513, 19, 64.89, 127.93, 0.35487, 2, 18, 70.31, 124.39, 0.36729, 19, 70.31, 135.07, 0.63271, 2, 18, 80.47, 126.67, 0.19886, 19, 80.47, 137.34, 0.80114, 2, 18, 95.66, 125.92, 0.10171, 19, 95.66, 136.6, 0.89829, 2, 18, 108.56, 122.02, 0.19647, 19, 108.56, 132.69, 0.80353, 2, 18, 117.62, 114.78, 0.36288, 19, 117.62, 125.46, 0.63712, 2, 18, 121.49, 104.86, 0.46208, 19, 121.49, 115.54, 0.53792, 2, 18, 121.69, 92.1, 0.59174, 19, 121.69, 102.78, 0.40826, 2, 18, 114.64, 85.44, 0.74078, 19, 114.64, 96.12, 0.25922, 2, 18, 106.9, 81.54, 0.86799, 19, 106.9, 92.21, 0.13201, 1, 18, 103.35, 77.75, 1, 1, 18, 109.67, 66.42, 1, 2, 18, 119.96, 66.84, 0.98349, 19, 119.96, 77.51, 0.01651, 2, 18, 132.78, 58.11, 0.81787, 19, 132.78, 68.78, 0.18213, 2, 18, 141.05, 47.77, 0.70315, 19, 141.05, 58.44, 0.29685, 2, 18, 146.38, 34.2, 0.62876, 19, 146.38, 44.88, 0.37124, 2, 18, 146.29, 22, 0.60882, 19, 146.29, 32.68, 0.39118, 2, 18, 144.11, 8.19, 0.63067, 19, 144.11, 18.86, 0.36933, 2, 18, 136.31, 1.81, 0.74414, 19, 136.31, 12.49, 0.25586, 2, 18, 126.93, -2.41, 0.88743, 19, 126.93, 8.27, 0.11257, 1, 18, 125.67, -20.51, 1, 2, 18, 121.63, -39.03, 0.92415, 19, 121.63, -28.36, 0.07585, 2, 18, 129.26, -42.12, 0.85626, 19, 129.26, -31.44, 0.14374, 2, 18, 140.76, -47.64, 0.65141, 19, 140.76, -36.96, 0.34859, 2, 18, 147.81, -54, 0.45337, 19, 147.81, -43.33, 0.54663, 2, 18, 153.4, -64.06, 0.22414, 19, 153.4, -53.39, 0.77586, 2, 18, 154.61, -72.28, 0.12863, 19, 154.61, -61.61, 0.87137, 2, 18, 152.64, -81.49, 0.06435, 19, 152.64, -70.82, 0.93565, 2, 18, 146.18, -87.13, 0.0759, 19, 146.18, -76.45, 0.9241, 2, 18, 138.88, -88.32, 0.11319, 19, 138.88, -77.64, 0.88681, 2, 18, 131.77, -85.67, 0.22246, 19, 131.77, -75, 0.77754, 2, 18, 126.5, -77.3, 0.41853, 19, 126.5, -66.62, 0.58147, 2, 18, 121.97, -65.4, 0.66246, 19, 121.97, -54.72, 0.33754, 2, 18, 114.11, -56.28, 0.99426, 19, 114.11, -45.61, 0.00574, 2, 18, 109.4, -62.28, 0.99314, 19, 109.4, -51.6, 0.00686, 2, 18, 104.08, -70.39, 0.99213, 19, 104.08, -59.71, 0.00787, 2, 18, 98.24, -77.33, 0.99754, 19, 98.24, -66.65, 0.00246, 2, 18, 94.36, -89.81, 0.60983, 19, 94.36, -79.13, 0.39017, 2, 18, 97.33, -95.07, 0.67939, 19, 97.33, -84.39, 0.32061, 2, 18, 103.43, -101.07, 0.58167, 19, 103.43, -90.39, 0.41833, 2, 18, 109.54, -105.3, 0.43988, 19, 109.54, -94.63, 0.56012, 2, 18, 115.81, -112.43, 0.3264, 19, 115.81, -101.75, 0.6736, 2, 18, 121.25, -119, 0.20757, 19, 121.25, -108.32, 0.79243, 2, 18, 124.38, -126.45, 0.2256, 19, 124.38, -115.78, 0.7744, 2, 18, 124.42, -132.77, 0.33257, 19, 124.42, -122.1, 0.66743, 2, 18, 123.55, -143.88, 0.27292, 19, 123.55, -133.21, 0.72708, 2, 18, 118.59, -152.98, 0.13068, 19, 118.59, -142.31, 0.86932, 1, 19, 109.99, -150.11, 1, 1, 19, 96.24, -151.15, 1, 2, 18, 85.73, -158.03, 0.09602, 19, 85.73, -147.35, 0.90398, 2, 18, 78.15, -145.04, 0.39368, 19, 78.15, -134.37, 0.60632, 2, 18, 73.51, -130.78, 0.70124, 19, 73.51, -120.11, 0.29876, 2, 18, 71.94, -119.42, 0.86966, 19, 71.94, -108.74, 0.13034, 2, 18, 68.6, -110.64, 0.95079, 19, 68.6, -99.96, 0.04921, 2, 18, 61.39, -108.45, 0.97473, 19, 61.39, -97.77, 0.02527, 2, 18, 55.07, -112.16, 0.95012, 19, 55.07, -101.49, 0.04988, 2, 18, 53.57, -125.98, 0.83978, 19, 53.57, -115.3, 0.16022, 2, 18, 51.29, -136.12, 0.7483, 19, 51.29, -125.44, 0.2517, 2, 18, 43.94, -150.35, 0.50798, 19, 43.94, -139.67, 0.49202, 2, 18, 33.03, -161.53, 0.20256, 19, 33.03, -150.85, 0.79744, 2, 18, 21.01, -165.66, 0.00552, 19, 21.01, -154.98, 0.99448, 1, 19, 8.04, -153.46, 1, 2, 18, -2.08, -151.12, 0.29991, 19, -2.08, -140.44, 0.70009, 2, 18, -8.02, -136, 0.61942, 19, -8.02, -125.33, 0.38058, 2, 18, -12.78, -126.03, 0.74609, 19, -12.78, -115.35, 0.25391, 2, 18, -31.03, -119.37, 0.81636, 19, -31.03, -108.69, 0.18364, 2, 18, -44.69, -117.31, 0.63451, 19, -44.69, -106.63, 0.36549, 2, 18, -56.87, -112.04, 0.59263, 19, -56.87, -101.36, 0.40737, 2, 18, -67.63, -113.63, 0.49938, 19, -67.63, -102.96, 0.50062, 2, 18, -76.03, -113.1, 0.44983, 19, -76.03, -102.43, 0.55017, 2, 18, -82.89, -105.42, 0.5575, 19, -82.89, -94.75, 0.4425, 2, 18, -87.96, -92.4, 0.81245, 19, -87.96, -81.72, 0.18755, 2, 18, -88.24, -80.61, 0.9861, 19, -88.24, -69.93, 0.0139, 2, 18, -95.71, -73.18, 0.99035, 19, -95.71, -62.5, 0.00965, 2, 18, -103.83, -87.19, 0.52724, 19, -103.83, -76.51, 0.47276, 1, 19, -112.87, -96.22, 1, 1, 19, -121.68, -104.97, 1, 1, 19, -136.77, -105.09, 1, 2, 18, -141.84, -109.31, 0.08531, 19, -141.84, -98.63, 0.91469, 2, 18, -144.69, -96.14, 0.33535, 19, -144.69, -85.46, 0.66465, 2, 18, -140.67, -85.7, 0.46148, 19, -140.67, -75.02, 0.53852, 2, 18, -135.95, -77.08, 0.55661, 19, -135.95, -66.41, 0.44339, 2, 18, -124.21, -70.98, 0.68332, 19, -124.21, -60.3, 0.31668, 2, 18, -111.76, -66.77, 0.77501, 19, -111.76, -56.09, 0.22499, 2, 18, -107.46, -60.29, 0.73603, 19, -107.46, -49.62, 0.26397, 2, 18, -108.21, -51.16, 0.51731, 19, -108.21, -40.48, 0.48269, 2, 18, -112.49, -52.79, 0.55489, 19, -112.49, -42.12, 0.44511, 2, 18, -121.27, -49.57, 0.48913, 19, -121.27, -38.89, 0.51087, 2, 18, -129.25, -38.22, 0.50107, 19, -129.25, -27.54, 0.49893, 2, 18, -132.01, -27.67, 0.61634, 19, -132.01, -17, 0.38366, 2, 18, -131.8, -13.11, 0.65964, 19, -131.8, -2.44, 0.34036, 2, 18, -127.78, -5.36, 0.65438, 19, -127.78, 5.31, 0.34562, 2, 18, -131.82, 1.43, 0.6746, 19, -131.82, 12.1, 0.3254, 2, 18, -135.8, 18.83, 0.90838, 19, -135.8, 29.5, 0.09162, 2, 18, -135.79, 36.43, 0.80863, 19, -135.79, 47.11, 0.19137, 2, 18, -131.21, 47.68, 0.71293, 19, -131.21, 58.35, 0.28707, 2, 18, -121.97, 60.35, 0.68699, 19, -121.97, 71.02, 0.31301, 2, 18, -110.44, 69.7, 0.78083, 19, -110.44, 80.37, 0.21917, 2, 18, -96.46, 76.64, 0.94418, 19, -96.46, 87.31, 0.05582, 2, 18, -89.34, 82.55, 0.67426, 19, -89.34, 93.22, 0.32574, 2, 18, -94.18, 88.9, 0.67915, 19, -94.18, 99.58, 0.32085, 2, 18, -103.39, 92.35, 0.53234, 19, -103.39, 103.02, 0.46766, 2, 18, -112.81, 99.57, 0.55156, 19, -112.81, 110.24, 0.44844, 2, 18, -120.79, 108.47, 0.28889, 19, -120.79, 119.15, 0.71111, 2, 18, -120.8, 122.84, 0.03582, 19, -120.8, 133.52, 0.96418, 1, 19, -114.81, 145.21, 1, 1, 19, -103.8, 152.76, 1, 2, 18, 101.18, -125.37, 0.71328, 19, 101.18, -114.69, 0.28672, 2, 18, 22.19, -131.76, 0.72168, 19, 22.19, -121.09, 0.27832, 2, 18, -124.9, -88.81, 0.48636, 19, -124.9, -78.13, 0.51364, 2, 18, 138.69, -65.46, 0.48188, 19, 138.69, -54.78, 0.51812, 2, 18, 122.35, 36.82, 0.90859, 19, 122.35, 47.5, 0.09141, 2, 18, 1.69, 0.84, 0.31422, 19, 1.69, 11.52, 0.68578, 2, 18, 94.94, 109.37, 0.81578, 19, 94.94, 120.04, 0.18422], "hull": 117, "edges": [10, 12, 12, 14, 14, 16, 16, 18, 22, 24, 24, 26, 34, 36, 58, 60, 60, 62, 72, 74, 152, 154, 172, 174, 174, 176, 176, 178, 210, 212, 218, 220, 224, 226, 226, 228, 228, 230, 230, 232, 166, 168, 168, 170, 170, 172, 182, 184, 184, 186, 186, 188, 188, 190, 190, 192, 192, 194, 194, 196, 200, 202, 202, 204, 196, 198, 198, 200, 158, 160, 160, 162, 154, 156, 156, 158, 162, 164, 164, 166, 146, 148, 142, 144, 144, 146, 140, 142, 134, 136, 132, 134, 136, 138, 138, 140, 128, 130, 130, 132, 126, 128, 122, 124, 124, 126, 118, 120, 120, 122, 114, 116, 116, 118, 110, 112, 112, 114, 108, 110, 178, 180, 180, 182, 102, 104, 100, 102, 94, 96, 104, 106, 106, 108, 96, 98, 98, 100, 90, 92, 92, 94, 86, 88, 88, 90, 82, 84, 84, 86, 74, 76, 76, 78, 78, 80, 80, 82, 68, 70, 70, 72, 62, 64, 64, 66, 66, 68, 54, 56, 56, 58, 50, 52, 52, 54, 46, 48, 48, 50, 44, 46, 244, 60, 40, 42, 42, 44, 36, 38, 38, 40, 30, 32, 32, 34, 26, 28, 28, 30, 18, 20, 20, 22, 40, 244, 148, 150, 150, 152, 156, 244, 160, 244, 162, 244, 158, 244, 6, 8, 8, 10, 2, 4, 4, 6, 2, 0, 0, 232, 220, 222, 222, 224, 216, 218, 212, 214, 214, 216, 208, 210, 206, 208, 204, 206, 244, 202], "width": 322, "height": 337}}, "blot_drop2": {"blot_drop2": {"width": 63, "height": 52}}, "blot_drop3": {"blot_drop2": {"width": 63, "height": 52}}, "blot_drop4": {"blot_drop2": {"width": 63, "height": 52}}, "blot_drop5": {"blot_drop1": {"rotation": -0.04, "width": 30, "height": 29}}, "blot_drop6": {"blot_drop2": {"width": 63, "height": 52}}, "blot_drop7": {"blot_drop2": {"width": 63, "height": 52}}, "blot_drop8": {"blot_drop1": {"rotation": -0.04, "width": 30, "height": 29}}, "blot_drop_s1": {"blot_drop1": {"rotation": -0.04, "width": 30, "height": 29}}, "blot_drop_s2": {"blot_drop1": {"rotation": -0.04, "width": 30, "height": 29}}, "body": {"body": {"type": "mesh", "uvs": [0.74141, 0.05265, 0.86511, 0.1547, 0.95072, 0.2988, 0.98133, 0.39622, 0.99807, 0.5, 0.99846, 0.63822, 0.98047, 0.80404, 0.94993, 0.89578, 0.88867, 0.96381, 0.76262, 1, 0.53878, 1, 0.22895, 1, 0.09112, 0.95409, 0.03221, 0.86905, 0, 0.7014, 0, 0.55804, 0.02514, 0.37095, 0.09112, 0.21301, 0.21717, 0.08909, 0.35999, 0.00598, 0.58709, 0, 0.53761, 0.49, 0.03575, 0.60906, 0.06873, 0.40739, 0.12175, 0.28104, 0.2478, 0.16684, 0.38563, 0.10367, 0.57766, 0.10853, 0.71196, 0.1304, 0.8439, 0.24217, 0.92048, 0.38067, 0.95582, 0.51673, 0.95347, 0.63093, 0.93933, 0.80831, 0.88749, 0.87877, 0.75202, 0.89578, 0.53407, 0.89578, 0.22777, 0.90307, 0.13235, 0.87634, 0.07698, 0.81316, 0.20892, 0.57019, 0.86158, 0.57748], "triangles": [40, 21, 37, 41, 35, 21, 41, 21, 28, 26, 21, 40, 10, 35, 9, 8, 9, 34, 11, 36, 10, 10, 36, 35, 12, 38, 11, 38, 37, 11, 11, 37, 36, 9, 35, 34, 8, 34, 7, 13, 39, 12, 12, 39, 38, 36, 37, 21, 26, 40, 25, 38, 40, 37, 36, 21, 35, 35, 41, 34, 29, 41, 28, 34, 33, 7, 7, 33, 6, 21, 27, 28, 34, 41, 33, 38, 39, 40, 13, 14, 39, 14, 22, 39, 39, 22, 40, 33, 32, 6, 33, 41, 32, 6, 32, 5, 14, 15, 22, 32, 31, 5, 31, 4, 5, 32, 41, 31, 22, 23, 40, 22, 15, 23, 41, 30, 31, 41, 29, 30, 23, 24, 40, 40, 24, 25, 15, 16, 23, 31, 3, 4, 31, 30, 3, 21, 26, 27, 24, 23, 17, 30, 2, 3, 30, 29, 2, 23, 16, 17, 29, 1, 2, 24, 18, 25, 24, 17, 18, 28, 0, 29, 29, 0, 1, 25, 19, 26, 25, 18, 19, 27, 20, 28, 28, 20, 0, 27, 26, 20, 26, 19, 20], "vertices": [1, 3, 166.74, -64.64, 1, 2, 3, 150.76, -105.6, 0.9854, 6, -100.74, -22.84, 0.0146, 2, 3, 127.94, -134.05, 0.94286, 6, -84.43, 9.78, 0.05714, 2, 3, 112.44, -144.28, 0.86603, 6, -71.44, 23.05, 0.13397, 2, 3, 95.89, -149.94, 0.77905, 6, -56.45, 32.08, 0.22095, 2, 3, 73.77, -150.26, 0.70857, 6, -34.9, 37.04, 0.29143, 2, 3, 47.19, -144.55, 0.6673, 6, -7.71, 37.06, 0.3327, 2, 3, 32.43, -134.59, 0.65079, 6, 8.82, 30.44, 0.34921, 2, 3, 21.37, -114.47, 0.68508, 6, 23.87, 13.1, 0.31492, 2, 3, 15.23, -72.92, 0.76381, 6, 38.63, -26.21, 0.23619, 2, 3, 14.6, 0.94, 0.8819, 6, 54.81, -98.29, 0.1181, 3, 3, 13.74, 103.18, 0.60932, 4, 40.82, 21.83, 0.36571, 6, 77.2, -198.05, 0.02497, 2, 3, 20.7, 148.73, 0.63429, 4, 24.02, -21.07, 0.36571, 2, 3, 34.14, 168.28, 0.63429, 4, 6.6, -37.19, 0.36571, 2, 3, 60.87, 179.14, 0.63429, 4, -21.86, -41.9, 0.36571, 2, 3, 83.81, 179.33, 0.81714, 4, -44.28, -37.05, 0.18286, 2, 3, 113.81, 171.29, 0.98286, 4, -71.78, -22.61, 0.01714, 1, 3, 139.27, 149.73, 1, 1, 3, 159.44, 108.3, 1, 1, 3, 173.14, 61.29, 1, 1, 3, 174.73, -13.64, 1, 2, 3, 96.2, 2.02, 0.27143, 8, 32.23, 3.47, 0.72857, 3, 3, 75.74, 167.46, 0.58457, 4, -33.81, -27.25, 0.20672, 8, 11.78, 168.92, 0.20871, 3, 3, 108.1, 156.85, 0.72693, 4, -63.04, -9.78, 0.04494, 8, 44.14, 158.31, 0.22813, 2, 3, 128.47, 139.53, 0.77801, 8, 64.5, 140.98, 0.22199, 2, 3, 147.09, 98.09, 0.78531, 8, 83.13, 99.54, 0.21469, 2, 3, 157.58, 52.69, 0.78643, 8, 93.62, 54.14, 0.21357, 2, 3, 157.34, -10.68, 0.78643, 8, 93.38, -9.23, 0.21357, 3, 3, 154.22, -55.03, 0.77555, 6, -93.47, -73, 0.01397, 8, 90.25, -53.58, 0.21048, 3, 3, 136.71, -98.72, 0.74739, 6, -85.55, -26.6, 0.04745, 8, 72.74, -97.27, 0.20516, 3, 3, 114.76, -124.18, 0.69128, 6, -69.47, 2.91, 0.11169, 8, 50.8, -122.72, 0.19703, 3, 3, 93.09, -136.02, 0.62619, 6, -50.78, 19.06, 0.17908, 8, 29.13, -134.57, 0.19473, 3, 3, 74.81, -135.4, 0.56498, 6, -32.78, 22.3, 0.23856, 8, 10.85, -133.95, 0.19645, 3, 3, 46.39, -130.98, 0.52873, 6, -4.07, 23.97, 0.26747, 8, -17.57, -129.52, 0.2038, 3, 3, 34.98, -113.97, 0.54342, 6, 10.68, 9.74, 0.24818, 8, -28.99, -112.51, 0.2084, 3, 3, 31.88, -69.28, 0.5926, 6, 23.13, -33.28, 0.18618, 8, -32.09, -67.83, 0.22122, 3, 3, 31.27, 2.63, 0.66615, 6, 38.88, -103.46, 0.09303, 8, -32.7, 4.09, 0.24082, 4, 3, 29.24, 103.7, 0.49774, 4, 25.58, 24.73, 0.28761, 6, 62.16, -201.82, 0.02205, 8, -34.72, 105.15, 0.19259, 3, 3, 33.25, 135.22, 0.50832, 4, 14.74, -5.14, 0.28761, 8, -30.71, 136.68, 0.20408, 3, 3, 43.2, 153.58, 0.50969, 4, 0.99, -20.86, 0.28761, 8, -20.76, 155.04, 0.2027, 3, 3, 82.45, 110.37, 0.15477, 4, -27.79, 29.92, 0.21505, 8, 18.48, 111.82, 0.63018, 3, 3, 83.11, -105.01, 0.24697, 6, -34.48, -9.16, 0.18816, 8, 19.14, -103.55, 0.56486], "hull": 21, "edges": [30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 0, 0, 2, 2, 4, 28, 30, 28, 26, 26, 24, 24, 22, 18, 16, 16, 14, 14, 12, 8, 10, 12, 10, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 18, 20, 20, 22, 72, 74, 74, 76, 76, 78, 78, 44, 4, 6, 6, 8], "width": 330, "height": 160}}, "body_outline": {"body_outline": {"type": "mesh", "uvs": [0.81918, 0.09587, 0.93728, 0.25509, 0.99999, 0.4438, 0.99999, 0.7219, 0.97035, 0.87325, 0.93728, 0.94818, 0.80501, 1, 0.50267, 1, 0.20033, 1, 0.08932, 0.96691, 0.03499, 0.8873, 0.00709, 0.74063, 0, 0.5, 0.0468, 0.29724, 0.14601, 0.13801, 0.2712, 0.05372, 0.44126, 0, 0.6562, 0, 0.22395, 0.57354, 0.76958, 0.57822, 0.53102, 0.56885], "triangles": [6, 7, 19, 7, 20, 19, 6, 19, 5, 8, 18, 7, 7, 18, 20, 20, 18, 15, 20, 15, 16, 8, 9, 18, 9, 10, 18, 5, 19, 4, 10, 11, 18, 4, 19, 3, 11, 12, 18, 19, 2, 3, 20, 17, 19, 19, 1, 2, 19, 0, 1, 19, 17, 0, 12, 13, 18, 13, 14, 18, 18, 14, 15, 20, 16, 17], "vertices": [2, 3, 165.83, -93.96, 0.99082, 6, -113.02, -37.39, 0.00918, 2, 3, 138.63, -134.7, 0.95006, 6, -95.02, 8.17, 0.04994, 2, 3, 106.16, -156.49, 0.81685, 6, -67.87, 36.31, 0.18315, 2, 3, 58.05, -156.9, 0.68736, 6, -20.93, 46.85, 0.31264, 2, 3, 31.78, -146.95, 0.6567, 6, 6.84, 42.66, 0.3433, 2, 3, 18.73, -135.72, 0.65917, 6, 21.98, 34.43, 0.34083, 2, 3, 9.38, -90.43, 0.73295, 6, 40.66, -7.87, 0.26705, 3, 3, 8.5, 13.27, 0.84889, 6, 63.38, -109.05, 0.10682, 4, 65.7, 108.39, 0.04429, 3, 3, 7.62, 116.97, 0.6162, 6, 86.09, -210.24, 0.01808, 4, 43.75, 7.03, 0.36571, 2, 3, 13.02, 155.09, 0.63429, 4, 30.1, -28.97, 0.36571, 2, 3, 26.64, 173.84, 0.63429, 4, 12.7, -44.27, 0.36571, 2, 3, 51.93, 183.63, 0.63429, 4, -14.12, -48.26, 0.36571, 2, 3, 93.53, 186.41, 0.85748, 4, -55.33, -41.82, 0.14252, 2, 3, 128.75, 170.66, 0.98892, 4, -86.21, -18.71, 0.01108, 1, 3, 156.58, 136.86, 1, 1, 3, 171.53, 94.05, 1, 1, 3, 181.31, 35.8, 1, 1, 3, 181.94, -37.92, 1, 4, 3, 81.46, 109.49, 0.16253, 8, 17.5, 110.94, 0.62198, 6, 12.33, -218.49, 0.00045, 4, -26.64, 30.56, 0.21504, 3, 3, 82.24, -77.66, 0.27879, 8, 18.28, -76.2, 0.57652, 6, -27.87, -35.71, 0.14469, 4, 3, 83.17, 4.18, 0.347, 8, 19.2, 5.63, 0.63003, 6, -11.53, -115.91, 0.01718, 4, -5.14, 133.67, 0.0058], "hull": 18, "edges": [24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 0, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 22, 24, 22, 20, 20, 18, 18, 16, 12, 14, 14, 16], "width": 343, "height": 173}}, "brow": {"brow": {"type": "mesh", "uvs": [0.17092, 0.13245, 0.26461, 0.2649, 0.30523, 0.31156, 0.34585, 0.35822, 0.38647, 0.40488, 0.42709, 0.45154, 0.47942, 0.48444, 0.53175, 0.51735, 0.58467, 0.53956, 0.63759, 0.56178, 0.68612, 0.55624, 0.73466, 0.55071, 0.77029, 0.52024, 0.80593, 0.48977, 0.87679, 0.37192, 0.92039, 0.3049, 1, 0.58929, 0.95489, 0.69412, 0.88526, 0.83099, 0.82228, 0.89717, 0.75929, 0.96335, 0.70341, 0.98167, 0.64753, 1, 0.58291, 0.99378, 0.51829, 0.98756, 0.45073, 0.93325, 0.38318, 0.87895, 0.33319, 0.83608, 0.28321, 0.79321, 0.23205, 0.73076, 0.18089, 0.6683, 0.08693, 0.53819, 0, 0.35822, 0.07724, 0], "triangles": [17, 15, 16, 14, 15, 17, 18, 14, 17, 13, 14, 18, 19, 13, 18, 12, 13, 19, 19, 20, 11, 19, 11, 12, 27, 28, 3, 27, 3, 4, 26, 27, 4, 5, 26, 4, 25, 5, 6, 25, 6, 7, 26, 5, 25, 21, 10, 11, 21, 11, 20, 24, 25, 7, 24, 7, 8, 23, 24, 8, 9, 23, 8, 22, 9, 10, 22, 23, 9, 10, 21, 22, 31, 33, 0, 32, 33, 31, 30, 0, 1, 31, 0, 30, 29, 30, 1, 29, 1, 2, 28, 29, 2, 28, 2, 3], "vertices": [3, 13, 2.59, -8.44, 0.80752, 11, 16.95, 32.23, 0.19246, 14, 10.24, 65.14, 2e-05, 3, 13, -1.06, -16.43, 0.6946, 11, 13.31, 24.24, 0.30518, 14, 6.6, 57.15, 0.00023, 3, 13, -2.33, -19.9, 0.57441, 11, 12.03, 20.77, 0.4245, 14, 5.32, 53.68, 0.00109, 3, 13, -3.61, -23.36, 0.45768, 11, 10.76, 17.31, 0.53806, 14, 4.04, 50.22, 0.00426, 3, 13, -4.89, -26.83, 0.35507, 11, 9.48, 13.85, 0.63366, 14, 2.77, 46.76, 0.01126, 3, 13, -6.16, -30.29, 0.25971, 11, 8.2, 10.38, 0.71335, 14, 1.49, 43.29, 0.02694, 3, 13, -7.05, -34.74, 0.1777, 11, 7.32, 5.93, 0.76881, 14, 0.61, 38.84, 0.05349, 3, 13, -7.93, -39.2, 0.11201, 11, 6.44, 1.47, 0.78824, 14, -0.28, 34.38, 0.09974, 3, 13, -8.52, -43.7, 0.06577, 11, 5.85, -3.03, 0.76475, 14, -0.86, 29.88, 0.16948, 3, 13, -9.1, -48.21, 0.03664, 11, 5.27, -7.54, 0.70273, 14, -1.45, 25.37, 0.26063, 3, 13, -8.91, -52.33, 0.01725, 11, 5.46, -11.66, 0.61624, 14, -1.26, 21.25, 0.36651, 3, 13, -8.72, -56.46, 0.00689, 11, 5.65, -15.78, 0.51032, 14, -1.07, 17.13, 0.48279, 3, 13, -7.84, -59.48, 0.00226, 11, 6.53, -18.81, 0.39032, 14, -0.19, 14.11, 0.60742, 3, 13, -6.96, -62.5, 0.00072, 11, 7.4, -21.83, 0.26664, 14, 0.69, 11.08, 0.73264, 3, 13, -3.61, -68.49, 0.00015, 11, 10.76, -27.82, 0.15425, 14, 4.04, 5.09, 0.8456, 3, 13, -1.7, -72.18, 3e-05, 11, 12.66, -31.51, 0.09653, 14, 5.95, 1.4, 0.90343, 3, 13, -9.61, -79.02, 2e-05, 11, 4.76, -38.35, 0.09472, 14, -1.96, -5.44, 0.90526, 3, 13, -12.58, -75.21, 0.00011, 11, 1.79, -34.54, 0.15054, 14, -4.92, -1.63, 0.84936, 3, 13, -16.46, -69.32, 0.0006, 11, -2.09, -28.65, 0.25876, 14, -8.81, 4.26, 0.74065, 3, 13, -18.36, -63.98, 0.00217, 11, -3.99, -23.31, 0.3865, 14, -10.7, 9.6, 0.61133, 3, 13, -20.26, -58.65, 0.00678, 11, -5.89, -17.98, 0.50782, 14, -12.6, 14.94, 0.4854, 3, 13, -20.81, -53.9, 0.01708, 11, -6.44, -13.23, 0.6141, 14, -13.16, 19.68, 0.36883, 3, 13, -21.36, -49.16, 0.03645, 11, -7, -8.49, 0.69877, 14, -13.71, 24.43, 0.26478, 3, 13, -21.23, -43.66, 0.06586, 11, -6.87, -2.99, 0.76301, 14, -13.58, 29.92, 0.17113, 3, 13, -21.11, -38.17, 0.11302, 11, -6.74, 2.5, 0.78638, 14, -13.45, 35.41, 0.1006, 3, 13, -19.63, -32.41, 0.18017, 11, -5.27, 8.26, 0.76596, 14, -11.98, 41.17, 0.05387, 3, 13, -18.16, -26.66, 0.26718, 11, -3.8, 14.01, 0.70533, 14, -10.51, 46.92, 0.02749, 3, 13, -17, -22.4, 0.36137, 11, -2.63, 18.27, 0.62716, 14, -9.35, 51.18, 0.01147, 3, 13, -15.83, -18.14, 0.47023, 11, -1.47, 22.53, 0.52532, 14, -8.18, 55.44, 0.00445, 3, 13, -14.12, -13.78, 0.58226, 11, 0.24, 26.89, 0.4166, 14, -6.47, 59.8, 0.00114, 3, 13, -12.41, -9.41, 0.70589, 11, 1.96, 31.26, 0.29386, 14, -4.76, 64.17, 0.00025, 3, 13, -8.84, -1.4, 0.81205, 11, 5.53, 39.27, 0.18794, 14, -1.18, 72.19, 2e-05, 3, 13, -3.86, 6.03, 0.86902, 11, 10.51, 46.7, 0.13098, 14, 3.79, 79.62, 0, 3, 13, 6.23, -0.45, 0.86677, 11, 20.59, 40.22, 0.13323, 14, 13.88, 73.14, 0], "hull": 34, "edges": [64, 66, 30, 32, 14, 48, 22, 40, 26, 28, 28, 30, 32, 34, 34, 36, 28, 34, 2, 0, 0, 66, 60, 62, 62, 64, 0, 62, 10, 12, 12, 14, 48, 50, 50, 52, 12, 50, 14, 16, 16, 18, 44, 46, 46, 48, 16, 46, 18, 20, 20, 22, 40, 42, 42, 44, 20, 42, 6, 8, 8, 10, 52, 54, 54, 56, 8, 54, 2, 4, 4, 6, 56, 58, 58, 60, 4, 58, 22, 24, 24, 26, 36, 38, 38, 40, 24, 38], "width": 85, "height": 28}}, "brow_base": {"brow_base": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 9, 37.07, -50.08, 1, 1, 9, 36.32, 37.92, 1, 1, 9, 80.32, 38.29, 1, 1, 9, 81.06, -49.71, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 88, "height": 44}}, "eye": {"eye": {"type": "mesh", "uvs": [0.77603, 0.084, 0.92134, 0.21743, 1, 0.42412, 1, 0.58894, 0.93547, 0.74591, 0.76997, 0.92382, 0.58631, 1, 0.43898, 1, 0.24725, 0.9212, 0.10597, 0.80347, 0, 0.62557, 0, 0.4215, 0.05551, 0.26191, 0.17862, 0.10232, 0.38247, 0, 0.59237, 0, 0.57622, 0.52615], "triangles": [16, 14, 15, 16, 15, 0, 16, 0, 1, 16, 1, 2, 16, 2, 3, 9, 10, 11, 4, 16, 3, 12, 16, 11, 16, 9, 11, 12, 13, 16, 16, 13, 14, 8, 9, 16, 5, 16, 4, 7, 8, 16, 16, 6, 7, 5, 6, 16], "vertices": [1, 9, 50.26, -18.29, 1, 1, 9, 43.14, -28.52, 1, 1, 9, 32.03, -34.12, 1, 1, 9, 23.13, -34.2, 1, 1, 9, 14.61, -29.75, 1, 1, 9, 4.91, -18.25, 1, 1, 9, 0.69, -5.43, 1, 1, 9, 0.6, 4.89, 1, 1, 9, 4.74, 18.34, 1, 1, 9, 11.01, 28.29, 1, 1, 9, 20.56, 35.78, 1, 1, 9, 31.58, 35.88, 1, 1, 9, 40.23, 32.07, 1, 1, 9, 48.92, 23.52, 1, 1, 9, 54.56, 9.3, 1, 1, 9, 54.69, -5.39, 1, 1, 9, 26.27, -4.5, 1], "hull": 16, "edges": [22, 24, 24, 26, 26, 28, 28, 30, 30, 0, 0, 2, 2, 4, 20, 22, 20, 18, 18, 16, 16, 14, 12, 14, 12, 10, 10, 8, 4, 6, 8, 6], "width": 70, "height": 54}}, "eye2": {"eye_red": {"type": "mesh", "uvs": [0.77603, 0.084, 0.92134, 0.21743, 1, 0.42412, 1, 0.58894, 0.93547, 0.74591, 0.76997, 0.92382, 0.58631, 1, 0.43898, 1, 0.24725, 0.9212, 0.10597, 0.80347, 0, 0.62557, 0, 0.4215, 0.05551, 0.26191, 0.17862, 0.10232, 0.38247, 0, 0.59237, 0, 0.57622, 0.52615], "triangles": [16, 14, 15, 16, 15, 0, 16, 0, 1, 16, 1, 2, 16, 2, 3, 9, 10, 11, 4, 16, 3, 12, 16, 11, 16, 9, 11, 12, 13, 16, 16, 13, 14, 8, 9, 16, 5, 16, 4, 7, 8, 16, 16, 6, 7, 5, 6, 16], "vertices": [1, 9, 50.26, -18.29, 1, 1, 9, 43.14, -28.52, 1, 1, 9, 32.03, -34.12, 1, 1, 9, 23.13, -34.2, 1, 1, 9, 14.61, -29.75, 1, 1, 9, 4.91, -18.25, 1, 1, 9, 0.69, -5.43, 1, 1, 9, 0.6, 4.89, 1, 1, 9, 4.74, 18.34, 1, 1, 9, 11.01, 28.29, 1, 1, 9, 20.56, 35.78, 1, 1, 9, 31.58, 35.88, 1, 1, 9, 40.23, 32.07, 1, 1, 9, 48.92, 23.52, 1, 1, 9, 54.56, 9.3, 1, 1, 9, 54.69, -5.39, 1, 1, 9, 26.27, -4.5, 1], "hull": 16, "edges": [22, 24, 24, 26, 26, 28, 28, 30, 30, 0, 0, 2, 2, 4, 20, 22, 20, 18, 18, 16, 16, 14, 12, 14, 12, 10, 10, 8, 4, 6, 8, 6], "width": 68, "height": 53}}, "eyelid_l": {"eyelid_l": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 9, 2.87, -27.37, 1, 1, 9, 2.4, 28.63, 1, 1, 9, 19.4, 28.77, 1, 1, 9, 19.87, -27.22, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 56, "height": 17}}, "eyelid_u": {"eyelid_u": {"type": "mesh", "uvs": [0.83749, 0.12298, 0.9268, 0.24584, 1, 0.40083, 1, 0.57792, 0.93527, 0.7556, 0.85021, 0.72913, 0.67868, 0.68755, 0.51212, 0.68188, 0.34555, 0.69511, 0.16842, 0.73939, 0.07337, 0.76316, 0, 0.6006, 0, 0.39327, 0.10601, 0.17213, 0.29172, 0.02848, 0.51074, 1e-05, 0.68865, 0.03024, 0.31015, 0.49156, 0.67731, 0.47644, 0.14074, 0.54765, 0.51641, 0.46321, 0.83865, 0.52718, 0.11877, 0.312, 0.29313, 0.21749, 0.5131, 0.19267, 0.69999, 0.21938, 0.83794, 0.27792], "triangles": [9, 10, 19, 19, 17, 8, 7, 20, 18, 20, 8, 17, 6, 18, 21, 6, 21, 5, 7, 18, 6, 7, 8, 20, 9, 19, 8, 10, 11, 19, 11, 12, 19, 12, 22, 19, 21, 2, 3, 21, 26, 2, 18, 25, 26, 23, 24, 20, 20, 24, 25, 26, 1, 2, 12, 13, 22, 22, 13, 23, 25, 0, 26, 26, 0, 1, 24, 16, 25, 25, 16, 0, 13, 14, 23, 23, 14, 24, 14, 15, 24, 24, 15, 16, 22, 23, 17, 19, 22, 17, 18, 26, 21, 17, 23, 20, 4, 5, 3, 5, 21, 3, 18, 20, 25], "vertices": [2, 9, 45.91, -20.6, 0.75227, 16, 24.25, -18.29, 0.24773, 2, 9, 40.69, -27.45, 0.73399, 16, 19.03, -25.13, 0.26601, 2, 9, 33.3, -32.82, 0.67007, 16, 11.64, -30.5, 0.32993, 2, 9, 24.34, -33.05, 0.57335, 16, 2.68, -30.73, 0.42665, 2, 9, 15.17, -30.69, 0.51057, 16, -6.49, -28.37, 0.48943, 2, 9, 17.16, -24.92, 0.47593, 16, -4.5, -22.6, 0.52407, 2, 9, 18.73, -10.67, 0.41769, 16, -2.93, -8.35, 0.58231, 2, 9, 18.91, -0.01, 0.41113, 16, -2.75, 2.31, 0.58887, 2, 9, 18.19, 10.65, 0.38732, 16, -3.48, 12.97, 0.61268, 2, 9, 15.96, 21.97, 0.41093, 16, -5.7, 24.28, 0.58907, 2, 9, 13.81, 31.29, 0.44897, 16, -7.85, 33.61, 0.55103, 2, 9, 23.14, 34.82, 0.51056, 16, 1.48, 37.14, 0.48944, 2, 9, 33.87, 34.76, 0.65144, 16, 12.21, 37.08, 0.34856, 2, 9, 45.32, 27.61, 0.73881, 16, 23.66, 29.92, 0.26119, 2, 9, 50.15, 14.36, 0.77879, 16, 28.49, 16.68, 0.22121, 2, 9, 51.64, 0.36, 0.76225, 16, 29.98, 2.68, 0.23775, 2, 9, 50.28, -11.04, 0.78619, 16, 28.62, -8.72, 0.21381, 2, 9, 27.94, 13, 0.45127, 16, 6.28, 15.31, 0.54873, 2, 9, 28.86, -10.49, 0.50711, 16, 7.2, -8.18, 0.49289, 2, 9, 25.15, 23.82, 0.44431, 16, 3.49, 26.13, 0.55569, 2, 9, 29.41, -0.19, 0.49403, 16, 7.75, 2.13, 0.50597, 2, 9, 26.23, -24.41, 0.54994, 16, 4.57, -22.09, 0.45006, 2, 9, 36.45, 25.32, 0.67406, 16, 14.79, 27.63, 0.32594, 2, 9, 41.08, 14.2, 0.6746, 16, 19.42, 16.51, 0.3254, 2, 9, 42.39, 0.13, 0.65714, 16, 20.73, 2.45, 0.34286, 2, 9, 41.21, -11.84, 0.67398, 16, 19.55, -9.52, 0.32602, 2, 9, 38.49, -21.62, 0.67088, 16, 16.82, -19.31, 0.32912], "hull": 17, "edges": [12, 10, 10, 8, 8, 6, 20, 22, 22, 24, 24, 26, 26, 28, 32, 0, 0, 2, 6, 4, 2, 4, 22, 38, 38, 34, 16, 18, 18, 20, 38, 18, 34, 40, 40, 36, 12, 14, 14, 16, 40, 14, 30, 32, 6, 42, 42, 36, 10, 42, 28, 30, 24, 44, 44, 46, 30, 48, 48, 40, 46, 48, 48, 50, 0, 52, 52, 42, 50, 52, 52, 4], "width": 64, "height": 48}}, "jaw": {"jaw": {"type": "mesh", "uvs": [0.13705, 0.10088, 0.18259, 0.20038, 0.29728, 0.12865, 0.43303, 0.07543, 0.62192, 0.06848, 0.76649, 0.14253, 0.86262, 0.23509, 0.90478, 0.17493, 0.92839, 0.07542, 0.94947, 0.07542, 0.97393, 0.20501, 0.97224, 0.38551, 1, 0.4503, 1, 0.56045, 0.98051, 0.75467, 0.92306, 0.92603, 0.86874, 1, 0.77049, 1, 0.66059, 0.96715, 0.55068, 0.88719, 0.47783, 0.9146, 0.39038, 0.93991, 0.24832, 0.97137, 0.13521, 0.92603, 0.06527, 0.80493, 0.02281, 0.61529, 0, 0.39939, 0.05188, 0.32997, 0.05441, 0.16104, 0.08139, 0, 0.11597, 0, 0.08419, 0.56701, 0.19472, 0.47876, 0.31645, 0.38663, 0.44743, 0.34331, 0.62075, 0.32709, 0.75617, 0.37884, 0.83846, 0.46822, 0.93103, 0.58112, 0.1219, 0.83985, 0.22304, 0.81633, 0.36415, 0.7615, 0.60361, 0.6752, 0.71503, 0.72695, 0.80074, 0.75518, 0.9156, 0.77399, 0.46273, 0.69744], "triangles": [32, 1, 33, 32, 27, 1, 11, 6, 7, 10, 11, 8, 8, 11, 7, 33, 3, 34, 10, 8, 9, 36, 5, 6, 35, 34, 4, 4, 34, 3, 35, 4, 5, 33, 2, 3, 11, 37, 6, 27, 0, 1, 0, 27, 28, 0, 29, 30, 29, 0, 28, 1, 2, 33, 36, 35, 5, 37, 36, 6, 32, 31, 27, 38, 11, 12, 26, 27, 31, 42, 34, 35, 39, 31, 32, 24, 25, 31, 40, 32, 33, 36, 43, 35, 37, 44, 36, 41, 33, 34, 17, 44, 16, 16, 45, 15, 16, 44, 45, 18, 43, 17, 17, 43, 44, 23, 40, 22, 22, 41, 21, 22, 40, 41, 19, 42, 18, 18, 42, 43, 20, 21, 46, 15, 45, 14, 24, 39, 23, 23, 39, 40, 21, 41, 46, 20, 46, 19, 19, 46, 42, 32, 40, 39, 39, 24, 31, 40, 33, 41, 45, 38, 14, 38, 45, 37, 46, 41, 34, 45, 44, 37, 44, 43, 36, 46, 34, 42, 43, 42, 35, 14, 38, 13, 25, 26, 31, 13, 38, 12, 38, 37, 11], "vertices": [1, 15, -90.63, -0.43, 1, 1, 15, -79.88, -8.99, 1, 1, 15, -52.82, -2.82, 1, 1, 15, -20.78, 1.76, 1, 1, 15, 23.8, 2.36, 1, 1, 15, 57.92, -4.01, 1, 1, 15, 80.61, -11.97, 1, 1, 15, 90.56, -6.8, 1, 1, 15, 96.13, 1.76, 1, 1, 15, 101.1, 1.76, 1, 1, 15, 106.87, -9.38, 1, 2, 3, 44.37, -107.46, 0.4, 15, 106.48, -24.91, 0.6, 2, 3, 38.85, -114.06, 0.94286, 15, 113.03, -30.48, 0.05714, 2, 3, 29.38, -114.14, 0.88571, 15, 113.03, -39.95, 0.11429, 2, 3, 12.64, -109.68, 0.90286, 15, 108.43, -56.65, 0.09714, 1, 3, -2.21, -96.25, 1, 1, 3, -8.68, -83.49, 1, 1, 3, -8.88, -60.3, 1, 1, 3, -6.27, -34.34, 1, 1, 3, 0.38, -8.34, 1, 1, 3, -2.12, 8.83, 1, 1, 3, -4.47, 29.45, 1, 1, 3, -7.46, 62.95, 1, 1, 3, -3.79, 89.68, 1, 1, 3, 6.49, 106.27, 1, 1, 3, 22.71, 116.43, 1, 1, 3, 41.23, 121.97, 1, 2, 3, 47.31, 109.77, 0.04, 15, -110.73, -20.13, 0.96, 1, 15, -110.13, -5.6, 1, 1, 15, -103.76, 8.25, 1, 1, 15, -95.6, 8.25, 1, 2, 3, 26.99, 101.98, 0.70286, 15, -103.1, -40.52, 0.29714, 2, 3, 34.8, 75.96, 0.52, 15, -77.02, -32.93, 0.48, 2, 3, 42.96, 47.3, 0.52, 15, -48.29, -25, 0.48, 2, 3, 46.95, 16.42, 0.52, 15, -17.38, -21.28, 0.48, 2, 3, 48.69, -24.47, 0.52, 15, 23.52, -19.88, 0.48, 2, 3, 44.51, -56.47, 0.52, 15, 55.48, -24.33, 0.48, 2, 3, 36.99, -75.95, 0.82857, 15, 74.9, -32.02, 0.17143, 2, 3, 27.47, -97.88, 0.98857, 15, 96.75, -41.73, 0.01143, 1, 3, 3.6, 92.88, 1, 1, 3, 5.82, 69.03, 1, 1, 3, 10.82, 35.77, 1, 2, 3, 18.72, -20.68, 0.95858, 15, 19.48, -49.82, 0.04142, 1, 3, 14.49, -47.01, 1, 1, 3, 12.24, -67.26, 1, 1, 3, 10.85, -94.38, 1, 2, 3, 16.53, 12.55, 0.99596, 15, -13.77, -51.73, 0.00404], "hull": 31, "edges": [52, 54, 54, 56, 56, 58, 58, 60, 60, 0, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 52, 50, 50, 48, 48, 46, 46, 44, 38, 36, 36, 34, 32, 34, 32, 30, 30, 28, 24, 26, 28, 26, 50, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 48, 78, 78, 80, 80, 82, 84, 86, 86, 88, 88, 90, 38, 40, 40, 42, 82, 92, 42, 44, 92, 84], "width": 236, "height": 86}}, "jaw_outline": {"jaw_outline": {"type": "mesh", "uvs": [0.16561, 0.10067, 0.20701, 0.17297, 0.36661, 0.08346, 0.49974, 0.05416, 0.6402, 0.06215, 0.78549, 0.13939, 0.84, 0.19265, 0.87459, 0.1314, 0.88507, 0.06481, 0.94168, 0.06215, 0.97103, 0.20064, 0.97312, 0.36577, 1, 0.4004, 1, 0.5503, 0.99283, 0.69413, 0.96096, 0.8138, 0.92192, 0.9274, 0.83109, 1, 0.74676, 0.99838, 0.61082, 0.96055, 0.53231, 0.91622, 0.44789, 0.93958, 0.34972, 0.97482, 0.27235, 0.98741, 0.1973, 0.9645, 0.10196, 0.88392, 0.05685, 0.7764, 0, 0.47998, 0, 0.3877, 0.05092, 0.31101, 0.05351, 0.15983, 0.09576, 0, 0.14233, 0, 0.22297, 0.53864, 0.51154, 0.52435, 0.83196, 0.58625], "triangles": [34, 3, 4, 2, 3, 34, 33, 1, 2, 33, 2, 34, 35, 5, 6, 7, 8, 9, 10, 7, 9, 11, 6, 7, 30, 0, 29, 31, 32, 0, 31, 0, 30, 1, 29, 0, 33, 29, 1, 6, 11, 35, 10, 11, 7, 5, 35, 4, 27, 28, 29, 11, 12, 13, 11, 13, 35, 26, 29, 33, 27, 29, 26, 13, 14, 35, 35, 14, 15, 25, 26, 33, 19, 20, 34, 16, 35, 15, 34, 22, 33, 21, 34, 20, 19, 34, 35, 4, 35, 34, 24, 25, 33, 33, 23, 24, 34, 21, 22, 22, 23, 33, 18, 19, 35, 17, 18, 35, 16, 17, 35], "vertices": [1, 15, -87.74, 4.38, 1, 1, 15, -77.43, -2.7, 1, 1, 15, -37.69, 6.07, 1, 1, 15, -4.54, 8.94, 1, 1, 15, 30.44, 8.16, 1, 1, 15, 66.61, 0.59, 1, 1, 15, 80.19, -4.63, 1, 1, 15, 88.8, 1.37, 1, 1, 15, 91.41, 7.9, 1, 1, 15, 105.51, 8.16, 1, 1, 15, 112.81, -5.42, 1, 2, 3, 47.74, -114.29, 0.31029, 15, 113.34, -21.6, 0.68971, 2, 3, 44.4, -121.01, 0.94286, 15, 120.03, -24.99, 0.05714, 2, 3, 29.71, -121.14, 0.88734, 15, 120.03, -39.68, 0.11266, 2, 3, 15.6, -119.47, 0.89753, 15, 118.24, -53.78, 0.10247, 2, 3, 3.8, -111.64, 0.92829, 15, 110.31, -65.51, 0.07171, 2, 3, -7.41, -102.01, 0.99996, 15, 100.59, -76.64, 4e-05, 1, 3, -14.72, -79.46, 1, 1, 3, -14.74, -58.46, 1, 1, 3, -11.31, -24.58, 1, 1, 3, -7.14, -4.99, 1, 1, 3, -9.6, 16.01, 1, 1, 3, -13.26, 40.42, 1, 1, 3, -14.66, 59.68, 1, 1, 3, -12.57, 78.38, 1, 1, 3, -4.88, 102.19, 1, 1, 3, 5.56, 113.51, 1, 1, 3, 34.49, 127.91, 1, 1, 3, 43.53, 127.99, 1, 2, 3, 51.16, 115.37, 0.02991, 15, -116.29, -16.23, 0.97009, 1, 15, -115.65, -1.42, 1, 1, 15, -105.13, 14.25, 1, 1, 15, -93.53, 14.25, 1, 2, 3, 29.21, 72.34, 0.62255, 15, -73.45, -38.54, 0.37745, 2, 3, 31.22, 0.51, 0.76478, 15, -1.6, -37.14, 0.23522, 2, 3, 25.83, -79.33, 0.9033, 15, 78.18, -43.21, 0.0967], "hull": 33, "edges": [56, 58, 58, 60, 60, 62, 62, 64, 64, 0, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 54, 56, 54, 52, 52, 50, 44, 46, 42, 44, 42, 40, 40, 38, 34, 32, 46, 48, 48, 50, 34, 36, 36, 38, 28, 30, 30, 32, 24, 26, 26, 28], "width": 249, "height": 98}}, "leg_l": {"leg_l": {"type": "mesh", "uvs": [0.7637, 0.08917, 0.93048, 0.25547, 1, 0.5, 1, 0.75, 1, 1, 0.65251, 1, 0.49407, 0.86057, 0.33563, 0.72113, 0.12437, 0.50161, 0, 0.27211, 0, 0.12243, 0.17996, 0, 0.58998, 0], "triangles": [2, 8, 1, 1, 8, 0, 12, 9, 11, 0, 9, 12, 0, 8, 9, 9, 10, 11, 5, 3, 4, 5, 6, 3, 6, 7, 3, 7, 2, 3, 8, 2, 7], "vertices": [2, 4, -34.78, 15.59, 0.14582, 3, 86.11, 125.89, 0.85418, 2, 4, -13.29, 22.88, 0.31612, 3, 66.76, 114.05, 0.68388, 2, 4, 15.7, 21.58, 0.5382, 3, 38.19, 108.94, 0.4618, 2, 4, 44.29, 15.4, 0.74822, 3, 8.94, 108.69, 0.25178, 2, 4, 72.87, 9.21, 0.88952, 3, -20.31, 108.45, 0.11048, 2, 4, 67.73, -14.57, 0.93242, 3, -20.51, 132.77, 0.06758, 2, 4, 49.44, -21.95, 0.87189, 3, -4.3, 144, 0.12811, 2, 4, 31.14, -29.34, 0.72339, 3, 11.92, 155.23, 0.27661, 2, 4, 2.91, -38.36, 0.51802, 3, 37.48, 170.23, 0.48198, 2, 4, -25.17, -41.19, 0.3116, 3, 64.26, 179.16, 0.6884, 2, 4, -42.29, -37.48, 0.15005, 3, 81.77, 179.31, 0.84995, 2, 4, -53.62, -22.14, 0.06399, 3, 96.2, 166.84, 0.93601, 2, 4, -47.55, 5.91, 0.05933, 3, 96.44, 138.14, 0.94067], "hull": 13, "edges": [20, 22, 22, 24, 24, 0, 0, 2, 2, 4, 18, 20, 18, 16, 16, 14, 8, 10, 4, 6, 6, 8, 10, 12, 12, 14], "width": 70, "height": 117}}, "leg_l_outline": {"leg_l_outline": {"type": "mesh", "uvs": [0.81986, 0.09612, 1, 0.3347, 1, 0.66735, 1, 0.83368, 0.93086, 0.99999, 0.60684, 0.99595, 0.41522, 0.84089, 0.17252, 0.58541, 0, 0.3064, 0, 0.14869, 0.19138, 0, 0.59569, 0], "triangles": [8, 9, 10, 0, 7, 8, 8, 11, 0, 1, 7, 0, 11, 8, 10, 2, 6, 1, 3, 6, 2, 6, 7, 1, 5, 6, 3, 4, 5, 3], "vertices": [2, 4, -37.12, 23.86, 0.14917, 3, 90.21, 118.33, 0.85083, 2, 4, -3.87, 31.96, 0.38503, 3, 59.56, 103.12, 0.61497, 2, 4, 38.07, 22.88, 0.69321, 3, 16.65, 102.76, 0.30679, 2, 4, 59.04, 18.34, 0.81486, 3, -4.8, 102.58, 0.18514, 2, 4, 78.79, 8.19, 0.88952, 3, -26.3, 108.13, 0.11048, 2, 4, 72.59, -17.98, 0.93242, 3, -26.01, 135.03, 0.06758, 2, 4, 49.68, -29.29, 0.85288, 3, -6.14, 151.1, 0.14712, 2, 4, 13.2, -42.01, 0.57827, 3, 26.64, 171.53, 0.42173, 2, 4, -25, -48.39, 0.3116, 3, 62.51, 186.15, 0.6884, 2, 4, -44.89, -44.08, 0.15005, 3, 82.85, 186.32, 0.84995, 2, 4, -60.27, -24.5, 0.06399, 3, 102.17, 170.6, 0.93601, 2, 4, -53.17, 8.3, 0.05933, 3, 102.45, 137.04, 0.94067], "hull": 12, "edges": [18, 20, 20, 22, 22, 0, 0, 2, 16, 18, 16, 14, 8, 10, 2, 4, 10, 12, 12, 14, 4, 6, 6, 8], "width": 83, "height": 129}}, "leg_r": {"leg_r": {"type": "mesh", "uvs": [1, 0.05734, 1, 0.23695, 0.88488, 0.48974, 0.6391, 0.78244, 0.36259, 1, 0.07584, 1, 0, 0.79642, 0, 0.59285, 0.10657, 0.29682, 0.27042, 0.11056, 0.5, 0, 0.81319, 0], "triangles": [2, 9, 1, 1, 9, 10, 11, 0, 1, 1, 10, 11, 2, 8, 9, 2, 7, 8, 3, 7, 2, 6, 7, 3, 4, 5, 6, 3, 4, 6], "vertices": [2, 6, -51.99, 33.73, 0.20056, 3, 91.18, -150.62, 0.79944, 2, 6, -31.49, 38.33, 0.28938, 3, 70.17, -150.8, 0.71062, 2, 6, -0.71, 36.27, 0.446, 3, 40.52, -142.3, 0.554, 2, 6, 36.79, 25.55, 0.63718, 3, 6.12, -123.91, 0.36282, 2, 6, 66.23, 10.62, 0.8036, 3, -19.52, -103.11, 0.1964, 2, 6, 71.01, -10.65, 0.8934, 3, -19.7, -81.32, 0.1066, 2, 6, 49.03, -21.49, 0.87605, 3, 4.07, -75.35, 0.12395, 2, 6, 25.79, -26.7, 0.75993, 3, 27.89, -75.15, 0.24007, 2, 6, -9.78, -26.39, 0.58152, 3, 62.59, -82.96, 0.41848, 2, 6, -33.77, -19.01, 0.39859, 3, 84.49, -95.23, 0.60141, 2, 6, -50.21, -4.82, 0.25947, 3, 97.57, -112.56, 0.74053, 2, 6, -55.43, 18.41, 0.19146, 3, 97.77, -136.37, 0.80854], "hull": 12, "edges": [14, 16, 16, 18, 18, 20, 20, 22, 22, 0, 0, 2, 2, 4, 4, 6, 6, 8, 12, 14, 8, 10, 12, 10], "width": 76, "height": 117}}, "leg_r_outline": {"leg_r_outline": {"type": "mesh", "uvs": [1, 0.08028, 1, 0.40378, 0.78016, 0.67876, 0.60825, 0.85264, 0.35928, 1, 0.08661, 1, 0, 0.75, 0, 0.5, 0.09846, 0.31078, 0.27037, 0.10859, 0.5, 0, 0.84536, 0], "triangles": [11, 0, 1, 10, 1, 9, 1, 10, 11, 1, 8, 9, 2, 8, 1, 7, 8, 2, 3, 6, 7, 2, 3, 7, 4, 5, 6, 3, 4, 6], "vertices": [2, 6, -55.6, 39.07, 0.20056, 3, 93.59, -156.6, 0.79944, 2, 6, -14.88, 48.21, 0.37016, 3, 51.86, -156.95, 0.62984, 2, 6, 23.96, 37.1, 0.56117, 3, 16.22, -137.91, 0.43883, 2, 6, 49.16, 27.25, 0.68892, 3, -6.34, -122.97, 0.31108, 2, 6, 72.51, 10.04, 0.81159, 3, -25.53, -101.22, 0.18841, 2, 6, 77.77, -13.37, 0.8934, 3, -25.74, -77.23, 0.1066, 2, 6, 47.97, -27.87, 0.8642, 3, 6.45, -69.33, 0.1358, 2, 6, 16.5, -34.94, 0.71373, 3, 38.7, -69.06, 0.28627, 2, 6, -9.21, -31.83, 0.58461, 3, 63.18, -77.52, 0.41539, 2, 6, -37.97, -22.78, 0.39399, 3, 89.39, -92.42, 0.60601, 2, 6, -56.07, -6.13, 0.25947, 3, 103.57, -112.51, 0.74053, 2, 6, -62.73, 23.52, 0.19343, 3, 103.83, -142.9, 0.80657], "hull": 12, "edges": [14, 16, 16, 18, 18, 20, 12, 14, 12, 10, 8, 10, 8, 6, 6, 4, 4, 2, 20, 22, 2, 0, 22, 0], "width": 88, "height": 129}}, "lip_u": {"lip_u": {"type": "mesh", "uvs": [1, 1, 0.85158, 0.75607, 0.79572, 0.67372, 0.73425, 0.58807, 0.67616, 0.53207, 0.58307, 0.49254, 0.51737, 0.47937, 0.38331, 0.49254, 0.24256, 0.61442, 0, 0.91178, 1e-05, 0.36634, 0.04324, 0, 0.08648, 1e-05, 0.17297, 0.18676, 0.38571, 0.02873, 0.58059, 0.03592, 0.68453, 0.1293, 0.78197, 0.20113, 0.85829, 0.14366, 0.93889, 0.10775, 0.99675, 0.38788, 0.06341, 0.62739, 0.20892, 0.43281, 0.38658, 0.2981, 0.58454, 0.29062, 0.74867, 0.40288, 0.86203, 0.53759, 0.51215, 0.29336, 0.67501, 0.3525, 0.80535, 0.47023, 0.8867, 0.62028], "triangles": [24, 15, 16, 27, 14, 15, 27, 15, 24, 23, 14, 27, 22, 13, 14, 28, 24, 16, 25, 16, 17, 28, 16, 25, 23, 22, 14, 29, 17, 18, 25, 17, 29, 6, 27, 24, 23, 8, 22, 7, 23, 27, 7, 27, 6, 5, 6, 24, 5, 24, 28, 4, 28, 25, 5, 28, 4, 26, 18, 19, 30, 26, 19, 29, 18, 26, 3, 4, 25, 7, 8, 23, 20, 30, 19, 12, 10, 11, 21, 12, 13, 21, 13, 22, 21, 10, 12, 29, 3, 25, 2, 29, 26, 2, 3, 29, 1, 2, 26, 1, 26, 30, 9, 10, 21, 8, 9, 21, 8, 21, 22, 30, 20, 0, 1, 30, 0], "vertices": [1, 3, 32.6, -116.11, 1, 1, 3, 44.99, -81.87, 1, 1, 3, 49.16, -68.99, 1, 1, 3, 53.5, -54.81, 1, 1, 3, 56.3, -41.43, 1, 1, 3, 58.17, -20, 1, 1, 3, 58.73, -4.88, 1, 1, 3, 57.78, 25.94, 1, 1, 3, 51.17, 58.26, 1, 1, 3, 35.23, 113.92, 1, 1, 3, 63.6, 114.15, 1, 1, 3, 82.73, 104.37, 1, 1, 3, 82.81, 94.43, 1, 1, 3, 73.27, 74.45, 1, 1, 3, 81.9, 25.59, 1, 1, 3, 81.91, -19.23, 1, 1, 3, 77.26, -43.17, 1, 1, 3, 73.71, -65.62, 1, 1, 3, 76.85, -83.15, 1, 1, 3, 78.87, -101.67, 1, 1, 3, 64.42, -115.1, 1, 1, 3, 50.15, 99.46, 1, 1, 3, 60.55, 66.08, 1, 1, 3, 67.9, 25.28, 1, 1, 3, 68.67, -20.25, 1, 1, 3, 63.16, -58.05, 1, 1, 3, 56.37, -84.18, 1, 1, 3, 68.39, -3.6, 1, 1, 3, 65.63, -41.08, 1, 1, 3, 59.76, -71.11, 1, 1, 3, 52.12, -89.89, 1], "hull": 21, "edges": [0, 40, 18, 20, 18, 42, 42, 44, 44, 46, 44, 16, 16, 14, 14, 12, 46, 54, 54, 48, 12, 10, 10, 8, 48, 56, 56, 50, 8, 56, 8, 6, 6, 50, 6, 4, 50, 58, 58, 52, 4, 58, 4, 2, 0, 60, 60, 52, 2, 60, 2, 0, 16, 18, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 24, 26, 20, 22, 22, 24], "width": 230, "height": 52}}, "mouth": {"mouth": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 3, 15.56, -112.26, 1, 1, 3, 13.63, 115.73, 1, 1, 3, 67.63, 116.19, 1, 1, 3, 69.56, -111.8, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 228, "height": 54}}, "pupil": {"pupil": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 12, -7.63, -7.8, 1, 1, 12, -7.77, 8.2, 1, 1, 12, 7.23, 8.33, 1, 1, 12, 7.37, -7.67, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 16, "height": 15}}}}], "animations": {"t0_405c80": {"slots": {"body_outline": {"rgba": [{"color": "405c80ff"}]}, "eyelid_u": {"rgba": [{"color": "ffffff00"}]}, "jaw_outline": {"rgba": [{"color": "405c80ff"}]}, "leg_l_outline": {"rgba": [{"color": "405c80ff"}]}, "leg_r_outline": {"rgba": [{"color": "405c80ff"}]}}}, "t0_eyeRed": {"slots": {"eye2": {"rgba": [{"color": "ffffffff"}]}}}, "t1_Death": {"slots": {"blot": {"rgba2": [{"light": "ff9520ff", "dark": "f9bb58", "curve": "stepped"}, {"time": 0.5, "light": "ff9520ff", "dark": "f9bb58"}, {"time": 1, "light": "ff952000", "dark": "f9bb58"}], "attachment": [{}, {"time": 0.2667, "name": "blot"}]}, "blot_drop2": {"rgba2": [{"light": "ff9520ff", "dark": "f9bb58", "curve": "stepped"}, {"time": 0.5, "light": "ff9520ff", "dark": "f9bb58"}, {"time": 0.6667, "light": "ff952000", "dark": "f9bb58"}], "attachment": [{}, {"time": 0.2667, "name": "blot_drop2"}, {"time": 0.5}]}, "blot_drop3": {"rgba2": [{"light": "ff9520ff", "dark": "f9bb58", "curve": "stepped"}, {"time": 0.5, "light": "ff9520ff", "dark": "f9bb58"}, {"time": 0.6667, "light": "ff952000", "dark": "f9bb58"}], "attachment": [{}, {"time": 0.2667, "name": "blot_drop2"}, {"time": 0.6}]}, "blot_drop4": {"rgba2": [{"light": "ff9520ff", "dark": "f9bb58", "curve": "stepped"}, {"time": 0.5, "light": "ff9520ff", "dark": "f9bb58"}, {"time": 0.6667, "light": "ff952000", "dark": "f9bb58"}], "attachment": [{}, {"time": 0.2667, "name": "blot_drop2"}, {"time": 0.5667}]}, "blot_drop5": {"rgba2": [{"light": "ff9520ff", "dark": "f9bb58", "curve": "stepped"}, {"time": 0.5, "light": "ff9520ff", "dark": "f9bb58"}, {"time": 0.6667, "light": "ff952000", "dark": "f9bb58"}], "attachment": [{}, {"time": 0.2667, "name": "blot_drop1"}, {"time": 0.5}]}, "blot_drop6": {"rgba2": [{"light": "ff9520ff", "dark": "f9bb58", "curve": "stepped"}, {"time": 0.5, "light": "ff9520ff", "dark": "f9bb58"}, {"time": 0.6667, "light": "ff952000", "dark": "f9bb58"}], "attachment": [{}, {"time": 0.2667, "name": "blot_drop2"}, {"time": 0.5667}]}, "blot_drop7": {"rgba2": [{"light": "ff9520ff", "dark": "f9bb58", "curve": "stepped"}, {"time": 0.5, "light": "ff9520ff", "dark": "f9bb58"}, {"time": 0.6667, "light": "ff952000", "dark": "f9bb58"}], "attachment": [{}, {"time": 0.2667, "name": "blot_drop2"}, {"time": 0.6333}]}, "blot_drop8": {"rgba2": [{"light": "ff9520ff", "dark": "f9bb58", "curve": "stepped"}, {"time": 0.5, "light": "ff9520ff", "dark": "f9bb58"}, {"time": 0.6667, "light": "ff952000", "dark": "f9bb58"}], "attachment": [{}, {"time": 0.3, "name": "blot_drop1"}, {"time": 0.6667}]}, "blot_drop_s1": {"rgba2": [{"light": "ff9520ff", "dark": "f9bb58", "curve": "stepped"}, {"time": 0.5, "light": "ff9520ff", "dark": "f9bb58"}, {"time": 0.6667, "light": "ff952000", "dark": "f9bb58"}], "attachment": [{}, {"time": 0.2667, "name": "blot_drop1"}, {"time": 0.4667}]}, "blot_drop_s2": {"rgba2": [{"light": "ff9520ff", "dark": "f9bb58", "curve": "stepped"}, {"time": 0.5, "light": "ff9520ff", "dark": "f9bb58"}, {"time": 0.6667, "light": "ff952000", "dark": "f9bb58"}], "attachment": [{}, {"time": 0.2667, "name": "blot_drop1"}, {"time": 0.4333}]}, "body": {"attachment": [{"time": 0.2667}]}, "body_outline": {"attachment": [{"time": 0.2667}]}, "brow": {"attachment": [{"time": 0.2667}]}, "brow_base": {"attachment": [{"time": 0.2667}]}, "eye": {"attachment": [{"time": 0.2667}]}, "eye2": {"attachment": [{"time": 0.2667}]}, "eyelid_l": {"attachment": [{"time": 0.2667}]}, "eyelid_u": {"rgba": [{"color": "ffffff00"}], "attachment": [{"time": 0.2667}]}, "jaw": {"attachment": [{"time": 0.2667}]}, "jaw_outline": {"attachment": [{"time": 0.2667}]}, "leg_l": {"attachment": [{"time": 0.2667}]}, "leg_l_outline": {"attachment": [{"time": 0.2667}]}, "leg_r": {"attachment": [{"time": 0.2667}]}, "leg_r_outline": {"attachment": [{"time": 0.2667}]}, "lip_u": {"attachment": [{"time": 0.2667}]}, "mouth": {"attachment": [{"time": 0.2667}]}, "pupil": {"attachment": [{"time": 0.2667}]}}, "bones": {"blot": {"translate": [{"x": -12.21, "y": 15.4, "curve": "stepped"}, {"time": 0.3, "x": -12.21, "y": 15.4, "curve": [0.422, -12.21, 0.544, -12.22, 0.376, 7.46, 0.544, 1.44]}, {"time": 0.6667, "x": -12.22, "y": 1.44}], "scale": [{"x": 0.7, "y": 0.7}, {"time": 0.2667, "x": 0.8, "y": 0.82, "curve": [0.288, 1.471, 0.378, 1.2, 0.288, 1.491, 0.378, 1.2]}, {"time": 0.4333, "x": 1.2, "y": 1.2}]}, "blot_drops_control": {"translate": [{}, {"time": 0.3333, "x": -0.54, "y": -0.54, "curve": [0.556, -0.54, 0.778, 0, 0.479, -0.67, 0.778, -16.59]}, {"time": 1, "y": -16.59}]}, "blot_drop2": {"rotate": [{"curve": "stepped"}, {"time": 0.2667, "curve": [0.333, 0, 0.4, -67.84]}, {"time": 0.4667, "value": -67.84}], "translate": [{}, {"time": 0.2667, "x": 41.44, "y": 7.91, "curve": [0.331, 77.65, 0.422, 273.93, 0.327, -38.99, 0.435, -211.23]}, {"time": 0.5, "x": 273.93, "y": -398.86}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4333, "curve": [0.478, 1, 0.522, 0.4, 0.478, 1, 0.522, 0.4]}, {"time": 0.5667, "x": 0.4, "y": 0.4}]}, "blot_drop3": {"rotate": [{}, {"time": 0.2667, "value": -35.08, "curve": [0.329, 32.76, 0.489, 77.81]}, {"time": 0.6, "value": 77.81}], "translate": [{}, {"time": 0.2667, "x": -74.68, "y": 24.95, "curve": [0.352, -115.07, 0.489, -322.02, 0.363, 261.05, 0.507, -337.68]}, {"time": 0.6, "x": -322.02, "y": -605.72}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4667, "curve": [0.511, 1, 0.556, 0.4, 0.511, 1, 0.556, 0.4]}, {"time": 0.6, "x": 0.4, "y": 0.4}]}, "blot_drop4": {"rotate": [{}, {"time": 0.2667, "value": 16.41, "curve": [0.363, 68.37, 0.467, 77.81]}, {"time": 0.5667, "value": 77.81}], "translate": [{}, {"time": 0.2667, "x": -48.78, "y": -9.58, "curve": [0.321, -164.38, 0.467, -211.51, 0.344, -53.5, 0.495, -476.89]}, {"time": 0.5667, "x": -211.51, "y": -781.84}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4333, "curve": [0.478, 1, 0.522, 0.4, 0.478, 1, 0.522, 0.4]}, {"time": 0.5667, "x": 0.4, "y": 0.4}]}, "blot_drop_s1": {"rotate": [{"curve": "stepped"}, {"time": 0.2667}], "translate": [{}, {"time": 0.2667, "x": 54.1, "y": 60.11, "curve": [0.334, 209.62, 0.4, 276.96, 0.333, -14.18, 0.39, -99.2]}, {"time": 0.4667, "x": 276.96, "y": -195.12}]}, "blot_drop_s2": {"rotate": [{"curve": "stepped"}, {"time": 0.2667}], "translate": [{}, {"time": 0.2667, "x": -53.64, "y": 83.69, "curve": [0.314, -164.57, 0.409, -315.66, 0.357, 159.13, 0.426, 84.28]}, {"time": 0.5, "x": -370.66, "y": -36.32}]}, "blot_drop5": {"rotate": [{}, {"time": 0.2667, "value": 103.14, "curve": [0.356, 103.14, 0.519, 97.56]}, {"time": 0.5333, "value": 97.56}], "translate": [{}, {"time": 0.2667, "x": 35.48, "y": -53.58, "curve": [0.355, 58.13, 0.467, 37.99, 0.344, -113.89, 0.483, -373.65]}, {"time": 0.5667, "x": 37.99, "y": -614.89}], "scale": [{"curve": "stepped"}, {"time": 0.4333, "curve": [0.478, 1, 0.522, 0.4, 0.478, 1, 0.522, 0.4]}, {"time": 0.5667, "x": 0.4, "y": 0.4}]}, "blot_drop_s3": {"rotate": [{"curve": "stepped"}, {"time": 0.2667}], "translate": [{}, {"time": 0.2667, "x": 10.55, "y": -19.73, "curve": [0.323, -6.01, 0.37, -21.01, 0.314, -79.55, 0.361, -201.42]}, {"time": 0.4333, "x": -31.65, "y": -390.13}]}, "blot_drop6": {"rotate": [{}, {"time": 0.2667, "value": -75.4, "curve": [0.309, -120.98, 0.393, -263.98]}, {"time": 0.5, "value": -261.68}], "translate": [{}, {"time": 0.2667, "x": 9.31, "y": 91.31, "curve": [0.358, 118.46, 0.511, 297.6, 0.322, 320.53, 0.481, 364.92]}, {"time": 0.6333, "x": 297.6, "y": -1347.33}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.5, "curve": [0.544, 1, 0.589, 0.4, 0.544, 1, 0.589, 0.4]}, {"time": 0.6333, "x": 0.4, "y": 0.4}]}, "blot_drop_s4": {"rotate": [{"curve": "stepped"}, {"time": 0.3}], "translate": [{}, {"time": 0.3, "x": -53.64, "y": 83.69, "curve": [0.388, -170.51, 0.54, -244.31, 0.46, -107.5, 0.576, -316.02]}, {"time": 0.6667, "x": -277.78, "y": -598.88}]}, "body": {"rotate": [{"value": 2.44, "curve": [0.011, 2.44, 0.022, 5.06]}, {"time": 0.0333, "value": 5.06, "curve": [0.056, 5.06, 0.078, -7.66]}, {"time": 0.1, "value": -7.66, "curve": [0.122, -7.66, 0.144, 7.48]}, {"time": 0.1667, "value": 7.48, "curve": [0.189, 7.48, 0.211, -5.12]}, {"time": 0.2333, "value": -5.12, "curve": [0.256, -5.12, 0.278, 9.84]}, {"time": 0.3, "value": 9.84}], "translate": [{"x": 3.5, "y": 0.15, "curve": "stepped"}, {"time": 0.0667, "x": 3.5, "y": 0.15, "curve": [0.1, 3.5, 0.133, 35.99, 0.1, 0.15, 0.133, 0.42]}, {"time": 0.1667, "x": 35.99, "y": 0.42}], "scale": [{"curve": [0.044, 1, 0.089, 0.86, 0.044, 1, 0.089, 0.74]}, {"time": 0.1333, "x": 0.86, "y": 0.74, "curve": [0.178, 0.86, 0.222, 1, 0.178, 0.74, 0.222, 1]}, {"time": 0.2667}]}, "main": {"translate": [{"curve": "stepped"}, {"time": 0.0667, "curve": [0.111, 0, 0.123, 0, 0.111, 0, 0.123, 39.11]}, {"time": 0.1667, "y": 39.11}]}, "mouth": {"rotate": [{}], "translate": [{"x": -2.96, "y": -0.28, "curve": [0.033, -2.96, 0.1, -16.45, 0.033, -0.28, 0.1, -1.54]}, {"time": 0.1333, "x": -16.45, "y": -1.54, "curve": [0.167, -16.45, 0.267, 12.61, 0.167, -1.54, 0.267, -1.48]}, {"time": 0.3, "x": 12.61, "y": -1.48}]}, "legR_ik": {"translate": [{"curve": [0.056, 0, 0.111, 49.26, 0.056, 0, 0.111, 0]}, {"time": 0.1667, "x": 49.26, "curve": [0.222, 49.26, 0.278, -75.17, 0.222, 0, 0.278, 5.84]}, {"time": 0.3333, "x": -75.17, "y": 5.84}]}, "legL_ik": {"translate": [{"curve": [0.056, 0, 0.111, -50.31, 0.056, 0, 0.111, 2.1]}, {"time": 0.1667, "x": -50.31, "y": 2.1, "curve": [0.222, -50.31, 0.278, 71.28, 0.222, 2.1, 0.278, 24.11]}, {"time": 0.3333, "x": 71.28, "y": 24.11}]}, "legR": {"translate": [{"x": 0.16, "y": 4.76, "curve": [0.078, 0.16, 0.156, 5.17, 0.078, 4.76, 0.156, -46.22]}, {"time": 0.2333, "x": 5.17, "y": -46.22}], "scale": [{"curve": [0.034, 1, 0.067, 1.019, 0.034, 1, 0.067, 1.019]}, {"time": 0.1, "x": 1.019, "y": 1.019, "curve": [0.167, 1.019, 0.234, 2.025, 0.167, 1.019, 0.234, 2.025]}, {"time": 0.3, "x": 2.025, "y": 2.025}]}, "legL": {"translate": [{"x": -0.08, "y": -4.75, "curve": [0.078, -0.08, 0.156, 20.24, 0.078, -4.75, 0.156, 56.93]}, {"time": 0.2333, "x": 20.24, "y": 56.93}], "scale": [{"curve": [0.034, 1, 0.067, 1.019, 0.034, 1, 0.067, 1.019]}, {"time": 0.1, "x": 1.019, "y": 1.019, "curve": [0.167, 1.019, 0.234, 2.371, 0.167, 1.019, 0.234, 2.371]}, {"time": 0.3, "x": 2.371, "y": 2.371}]}, "face": {"translatex": [{"value": -5.9}], "translatey": [{"value": 0.2}], "scale": [{"curve": [0.067, 1, 0.133, 0.875, 0.067, 1, 0.133, 0.875]}, {"time": 0.2, "x": 0.875, "y": 0.875, "curve": [0.244, 0.875, 0.289, 1.043, 0.244, 0.875, 0.289, 1.043]}, {"time": 0.3333, "x": 1.043, "y": 1.043}]}, "eye2": {"rotate": [{"curve": [0.078, 0, 0.156, 43.21]}, {"time": 0.2333, "value": 43.21}], "translate": [{"curve": [0.078, 0, 0.156, -8.38, 0.078, 0, 0.156, 3.5]}, {"time": 0.2333, "x": -8.38, "y": 3.5}]}, "brow_m": {"translate": [{"curve": [0.078, 0, 0.156, -13.63, 0.078, 0, 0.156, -0.88]}, {"time": 0.2333, "x": -13.63, "y": -0.88}]}, "body_cntrl": {"rotate": [{}]}, "eye_up_closed": {"translate": [{"x": 35.51}]}, "brow": {"translate": [{"x": -5.41, "curve": [0.111, -5.41, 0.122, 14.65, 0.111, 0, 0.122, -0.43]}, {"time": 0.2333, "x": 14.65, "y": -0.43}], "scale": [{"y": 0.705}]}, "pupil": {"translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": [0.178, 0, 0.189, 6.17, 0.178, 0, 0.189, 4.45]}, {"time": 0.2, "x": 6.17, "y": 4.45}], "scale": [{"time": 0.1667, "curve": [0.211, 1, 0.222, 2, 0.211, 1, 0.222, 2]}, {"time": 0.2667, "x": 2, "y": 2}]}, "eye": {"scale": [{"time": 0.2, "x": 1.502, "y": 1.502}]}, "eye3": {"rotate": [{"curve": [0.078, 0, 0.156, -59.89]}, {"time": 0.2333, "value": -59.89}], "translate": [{"curve": [0.078, 0, 0.156, -6.89, 0.078, 0, 0.156, -2.14]}, {"time": 0.2333, "x": -6.89, "y": -2.14}]}}}, "t1_HitReaction": {"slots": {"eye2": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.2333, "color": "ffffff00", "curve": [0.378, 1, 0.522, 1, 0.378, 1, 0.522, 1, 0.378, 1, 0.522, 1, 0.378, 0, 0.522, 1]}, {"time": 0.6667, "color": "ffffffff"}], "attachment": [{"name": "eye_red"}]}, "eyelid_u": {"rgba": [{"color": "ffffff00"}]}}, "bones": {"body": {"rotate": [{"value": 2.44, "curve": [0.022, 2.44, 0.044, -0.38]}, {"time": 0.0667, "value": -0.38, "curve": [0.111, -0.38, 0.156, 8.03]}, {"time": 0.2, "value": 8.03, "curve": [0.267, 8.03, 0.333, -2.42]}, {"time": 0.4, "value": -2.42, "curve": [0.433, -2.42, 0.467, 5.74]}, {"time": 0.5, "value": 5.74, "curve": [0.522, 5.74, 0.544, 0.03]}, {"time": 0.5667, "value": 0.03, "curve": [0.589, 0.03, 0.611, 4.23]}, {"time": 0.6333, "value": 4.23, "curve": [0.656, 4.23, 0.678, 2.44]}, {"time": 0.7, "value": 2.44}], "translate": [{"x": 3.5, "y": 0.15}], "scale": [{}]}, "mouth": {"rotate": [{}], "translate": [{"x": -2.96, "y": -0.28, "curve": [0.033, -0.09, 0.067, 5.64, 0.033, -0.37, 0.067, -0.57]}, {"time": 0.1, "x": 5.64, "y": -0.57, "curve": "stepped"}, {"time": 0.1333, "x": 5.64, "y": -0.57, "curve": [0.2, 5.64, 0.267, -0.09, 0.2, -0.57, 0.267, -0.37]}, {"time": 0.3333, "x": -2.96, "y": -0.28}]}, "legR_ik": {"translate": [{"curve": [0.011, 1.42, 0.022, 9.49, 0.011, 0, 0.022, 0]}, {"time": 0.0333, "x": 9.49, "curve": [0.067, 9.49, 0.1, -31.4, 0.067, 0, 0.1, 0]}, {"time": 0.1333, "x": -31.4, "curve": [0.167, -31.4, 0.2, 5.06, 0.156, 0, 0.211, 0]}, {"time": 0.2333, "x": 5.06, "curve": [0.256, 5.06, 0.278, -18.3, 0.267, 0, 0.267, 0]}, {"time": 0.3, "x": -18.3, "curve": [0.333, -18.3, 0.367, -10.36, 0.333, 0, 0.367, 0]}, {"time": 0.4}]}, "legL_ik": {"translate": [{"curve": [0.033, 0, 0.067, 33.2, 0.033, 0, 0.067, 0]}, {"time": 0.1, "x": 33.2, "curve": [0.133, 33.2, 0.167, -15.6, 0.122, 0, 0.178, 0]}, {"time": 0.2, "x": -15.6, "curve": [0.222, -15.6, 0.244, 11.77, 0.233, 0, 0.233, 0]}, {"time": 0.2667, "x": 11.77, "curve": [0.3, 11.77, 0.333, 0, 0.3, 0, 0.333, 0]}, {"time": 0.3667}]}, "legR": {"translate": [{"x": 0.16, "y": 4.76}]}, "legL": {"translate": [{"x": -0.08, "y": -4.75}]}, "face": {"translatex": [{"value": -5.9, "curve": [0.044, -5.9, 0.089, -6.45]}, {"time": 0.1333, "value": -6.45, "curve": "stepped"}, {"time": 0.1667, "value": -6.45, "curve": [0.222, -6.45, 0.278, -5.9]}, {"time": 0.3333, "value": -5.9}], "translatey": [{"value": 0.2, "curve": [0.044, 0.2, 0.089, 0.22]}, {"time": 0.1333, "value": 0.22, "curve": "stepped"}, {"time": 0.1667, "value": 0.22, "curve": [0.222, 0.22, 0.278, 0.2]}, {"time": 0.3333, "value": 0.2}]}, "eye_up_closed": {"translate": [{"x": 35.51}]}, "main": {"translatex": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.4667, "curve": "stepped"}, {"time": 0.6, "curve": "stepped"}, {"time": 0.7333}], "translatey": [{"curve": [0.03, 43.35, 0.067, 72.17]}, {"time": 0.1, "value": 75.97, "curve": [0.111, 77.24, 0.122, 77.77]}, {"time": 0.1333, "value": 77.77, "curve": [0.244, 77.77, 0.4, 20.05]}, {"time": 0.4667, "curve": [0.487, 4.51, 0.55, 13.66]}, {"time": 0.6, "value": 13.66, "curve": [0.645, 13.66, 0.713, 4.51]}, {"time": 0.7333}], "scale": [{"curve": [0.022, 1, 0.044, 0.86, 0.022, 1, 0.044, 1.14]}, {"time": 0.0667, "x": 0.86, "y": 1.14, "curve": [0.078, 0.86, 0.089, 1, 0.078, 1.14, 0.089, 1]}, {"time": 0.1, "x": 1.02, "y": 0.98, "curve": [0.111, 1.04, 0.122, 1.04, 0.111, 0.96, 0.122, 0.96]}, {"time": 0.1333, "x": 1.04, "y": 0.96, "curve": [0.178, 1.04, 0.222, 0.84, 0.178, 0.96, 0.222, 1.16]}, {"time": 0.2667, "x": 0.84, "y": 1.16, "curve": [0.311, 0.84, 0.356, 0.9, 0.311, 1.16, 0.356, 1.1]}, {"time": 0.4, "x": 0.9, "y": 1.1, "curve": [0.422, 0.9, 0.444, 1.023, 0.422, 1.1, 0.444, 0.977]}, {"time": 0.4667, "x": 1.023, "y": 0.977, "curve": [0.489, 1.023, 0.511, 0.981, 0.489, 0.977, 0.511, 1.019]}, {"time": 0.5333, "x": 0.981, "y": 1.019, "curve": [0.556, 0.981, 0.578, 1, 0.556, 1.019, 0.578, 1]}, {"time": 0.6, "curve": [0.622, 1, 0.644, 0.987, 0.622, 1, 0.644, 1.013]}, {"time": 0.6667, "x": 0.987, "y": 1.013, "curve": [0.689, 0.987, 0.711, 1, 0.689, 1.013, 0.711, 1]}, {"time": 0.7333}]}, "brow": {"translate": [{"x": -5.41, "curve": [0.056, -5.41, 0.111, 2.39, 0.056, 0, 0.111, -0.27]}, {"time": 0.1667, "x": 2.39, "y": -0.27, "curve": [0.222, 2.39, 0.278, -5.41, 0.222, -0.27, 0.278, 0]}, {"time": 0.3333, "x": -5.41}], "scale": [{"y": 0.705}]}, "eye": {"translate": [{}], "scale": [{}]}, "pupil": {"translate": [{"x": -7.35}, {"time": 0.1333, "x": 5.53, "y": -0.19, "curve": "stepped"}, {"time": 0.1667, "x": 5.53, "y": -0.19, "curve": [0.233, 5.53, 0.3, -7.35, 0.233, -0.19, 0.3, 0]}, {"time": 0.3667, "x": -7.35}]}}}, "t1_IDLE": {"slots": {"eyelid_u": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.5333, "color": "ffffff00", "curve": [0.544, 1, 0.556, 1, 0.544, 1, 0.556, 1, 0.544, 1, 0.556, 1, 0.544, 0, 0.556, 1]}, {"time": 0.5667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.8, "color": "ffffffff", "curve": [0.811, 1, 0.822, 1, 0.811, 1, 0.822, 1, 0.811, 1, 0.822, 1, 0.811, 1, 0.822, 0]}, {"time": 0.8333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.8333, "color": "ffffff00", "curve": [1.844, 1, 1.856, 1, 1.844, 1, 1.856, 1, 1.844, 1, 1.856, 1, 1.844, 0, 1.856, 1]}, {"time": 1.8667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.1, "color": "ffffffff", "curve": [2.111, 1, 2.122, 1, 2.111, 1, 2.122, 1, 2.111, 1, 2.122, 1, 2.111, 1, 2.122, 0]}, {"time": 2.1333, "color": "ffffff00", "curve": "stepped"}, {"time": 3.2, "color": "ffffff00", "curve": [3.211, 1, 3.222, 1, 3.211, 1, 3.222, 1, 3.211, 1, 3.222, 1, 3.211, 0, 3.222, 1]}, {"time": 3.2333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.4667, "color": "ffffffff", "curve": [3.478, 1, 3.489, 1, 3.478, 1, 3.489, 1, 3.478, 1, 3.489, 1, 3.478, 1, 3.489, 0]}, {"time": 3.5, "color": "ffffff00"}]}}, "bones": {"body": {"rotate": [{"value": 2.44, "curve": [0.023, 2.66, 0.045, 2.75]}, {"time": 0.0667, "value": 2.58, "curve": [0.178, 2.58, 0.289, -0.87]}, {"time": 0.4, "value": -0.87, "curve": [0.5, -0.87, 0.6, 3.35]}, {"time": 0.7, "value": 2.58, "curve": [0.811, 1.73, 0.922, -0.87]}, {"time": 1.0333, "value": -0.87, "curve": [1.111, -0.87, 1.189, 1.7]}, {"time": 1.2667, "value": 2.44, "curve": [1.289, 2.66, 1.311, 2.75]}, {"time": 1.3333, "value": 2.58, "curve": [1.444, 2.58, 1.556, -0.87]}, {"time": 1.6667, "value": -0.87, "curve": [1.767, -0.87, 1.867, 3.35]}, {"time": 1.9667, "value": 2.58, "curve": [2.078, 1.73, 2.189, -0.87]}, {"time": 2.3, "value": -0.87, "curve": [2.378, -0.87, 2.456, 1.68]}, {"time": 2.5333, "value": 2.44, "curve": "stepped"}, {"time": 2.5667, "value": 2.44, "curve": [2.589, 2.66, 2.611, 2.75]}, {"time": 2.6333, "value": 2.58, "curve": [2.744, 2.58, 2.856, -0.87]}, {"time": 2.9667, "value": -0.87, "curve": [3.067, -0.87, 3.167, 3.35]}, {"time": 3.2667, "value": 2.58, "curve": [3.378, 1.73, 3.489, -0.87]}, {"time": 3.6, "value": -0.87, "curve": [3.678, -0.87, 3.756, 1.7]}, {"time": 3.8333, "value": 2.44, "curve": [3.856, 2.66, 3.878, 2.75]}, {"time": 3.9, "value": 2.58, "curve": [4.011, 2.58, 4.122, -0.87]}, {"time": 4.2333, "value": -0.87, "curve": [4.333, -0.87, 4.433, 3.35]}, {"time": 4.5333, "value": 2.58, "curve": [4.644, 1.73, 4.756, -0.87]}, {"time": 4.8667, "value": -0.87, "curve": [4.945, -0.87, 5.023, 1.68]}, {"time": 5.1, "value": 2.44}], "translate": [{"curve": [0.1, 0, 0.2, 0, 0.1, 0, 0.2, 7.26]}, {"time": 0.3, "y": 7.26, "curve": "stepped"}, {"time": 0.3333, "y": 7.26, "curve": [0.433, 0, 0.533, 0, 0.433, 7.26, 0.533, 0]}, {"time": 0.6333, "curve": [0.733, 0, 0.833, 0, 0.733, 0, 0.833, 7.26]}, {"time": 0.9333, "y": 7.26, "curve": "stepped"}, {"time": 0.9667, "y": 7.26, "curve": [1.067, 0, 1.167, 0, 1.067, 7.26, 1.167, 0]}, {"time": 1.2667, "curve": [1.367, 0, 1.467, 0, 1.367, 0, 1.467, 7.26]}, {"time": 1.5667, "y": 7.26, "curve": "stepped"}, {"time": 1.6, "y": 7.26, "curve": [1.7, 0, 1.8, 0, 1.7, 7.26, 1.8, 0]}, {"time": 1.9, "curve": [2, 0, 2.1, 0, 2, 0, 2.1, 7.26]}, {"time": 2.2, "y": 7.26, "curve": "stepped"}, {"time": 2.2333, "y": 7.26, "curve": [2.333, 0, 2.433, 0, 2.333, 7.26, 2.433, 0]}, {"time": 2.5333, "curve": "stepped"}, {"time": 2.5667, "curve": [2.667, 0, 2.767, 0, 2.667, 0, 2.767, 7.26]}, {"time": 2.8667, "y": 7.26, "curve": "stepped"}, {"time": 2.9, "y": 7.26, "curve": [3, 0, 3.1, 0, 3, 7.26, 3.1, 0]}, {"time": 3.2, "curve": [3.3, 0, 3.4, 0, 3.3, 0, 3.4, 7.26]}, {"time": 3.5, "y": 7.26, "curve": "stepped"}, {"time": 3.5333, "y": 7.26, "curve": [3.633, 0, 3.733, 0, 3.633, 7.26, 3.733, 0]}, {"time": 3.8333, "curve": [3.933, 0, 4.033, 0, 3.933, 0, 4.033, 7.26]}, {"time": 4.1333, "y": 7.26, "curve": "stepped"}, {"time": 4.1667, "y": 7.26, "curve": [4.267, 0, 4.367, 0, 4.267, 7.26, 4.367, 0]}, {"time": 4.4667, "curve": [4.567, 0, 4.667, 0, 4.567, 0, 4.667, 7.26]}, {"time": 4.7667, "y": 7.26, "curve": "stepped"}, {"time": 4.8, "y": 7.26, "curve": [4.9, 0, 5, 0, 4.9, 7.26, 5, 0]}, {"time": 5.1}], "scale": [{"curve": [0.056, 1, 0.111, 1.02, 0.056, 1, 0.111, 0.98]}, {"time": 0.1667, "x": 1.02, "y": 0.98, "curve": [0.211, 1.02, 0.256, 1, 0.211, 0.98, 0.256, 1]}, {"time": 0.3, "curve": [0.356, 1, 0.411, 1.02, 0.356, 1, 0.411, 0.98]}, {"time": 0.4667, "x": 1.02, "y": 0.98, "curve": [0.522, 1.02, 0.578, 1, 0.522, 0.98, 0.578, 1]}, {"time": 0.6333, "curve": [0.678, 1, 0.722, 1.02, 0.678, 1, 0.722, 0.98]}, {"time": 0.7667, "x": 1.02, "y": 0.98, "curve": [0.822, 1.02, 0.878, 1, 0.822, 0.98, 0.878, 1]}, {"time": 0.9333, "curve": [0.989, 1, 1.044, 1.02, 0.989, 1, 1.044, 0.98]}, {"time": 1.1, "x": 1.02, "y": 0.98, "curve": [1.156, 1.02, 1.211, 1, 1.156, 0.98, 1.211, 1]}, {"time": 1.2667, "curve": [1.322, 1, 1.378, 1.02, 1.322, 1, 1.378, 0.98]}, {"time": 1.4333, "x": 1.02, "y": 0.98, "curve": [1.478, 1.02, 1.522, 1, 1.478, 0.98, 1.522, 1]}, {"time": 1.5667, "curve": [1.622, 1, 1.678, 1.02, 1.622, 1, 1.678, 0.98]}, {"time": 1.7333, "x": 1.02, "y": 0.98, "curve": [1.789, 1.02, 1.844, 1, 1.789, 0.98, 1.844, 1]}, {"time": 1.9, "curve": [1.944, 1, 1.989, 1.02, 1.944, 1, 1.989, 0.98]}, {"time": 2.0333, "x": 1.02, "y": 0.98, "curve": [2.089, 1.02, 2.144, 1, 2.089, 0.98, 2.144, 1]}, {"time": 2.2, "curve": [2.256, 1, 2.311, 1.02, 2.256, 1, 2.311, 0.98]}, {"time": 2.3667, "x": 1.02, "y": 0.98, "curve": [2.422, 1.02, 2.478, 1, 2.422, 0.98, 2.478, 1]}, {"time": 2.5333, "curve": "stepped"}, {"time": 2.5667, "curve": [2.622, 1, 2.678, 1.02, 2.622, 1, 2.678, 0.98]}, {"time": 2.7333, "x": 1.02, "y": 0.98, "curve": [2.778, 1.02, 2.822, 1, 2.778, 0.98, 2.822, 1]}, {"time": 2.8667, "curve": [2.922, 1, 2.978, 1.02, 2.922, 1, 2.978, 0.98]}, {"time": 3.0333, "x": 1.02, "y": 0.98, "curve": [3.089, 1.02, 3.144, 1, 3.089, 0.98, 3.144, 1]}, {"time": 3.2, "curve": [3.244, 1, 3.289, 1.02, 3.244, 1, 3.289, 0.98]}, {"time": 3.3333, "x": 1.02, "y": 0.98, "curve": [3.389, 1.02, 3.444, 1, 3.389, 0.98, 3.444, 1]}, {"time": 3.5, "curve": [3.556, 1, 3.611, 1.02, 3.556, 1, 3.611, 0.98]}, {"time": 3.6667, "x": 1.02, "y": 0.98, "curve": [3.722, 1.02, 3.778, 1, 3.722, 0.98, 3.778, 1]}, {"time": 3.8333, "curve": [3.889, 1, 3.944, 1.02, 3.889, 1, 3.944, 0.98]}, {"time": 4, "x": 1.02, "y": 0.98, "curve": [4.044, 1.02, 4.089, 1, 4.044, 0.98, 4.089, 1]}, {"time": 4.1333, "curve": [4.189, 1, 4.244, 1.02, 4.189, 1, 4.244, 0.98]}, {"time": 4.3, "x": 1.02, "y": 0.98, "curve": [4.356, 1.02, 4.411, 1, 4.356, 0.98, 4.411, 1]}, {"time": 4.4667, "curve": [4.511, 1, 4.556, 1.02, 4.511, 1, 4.556, 0.98]}, {"time": 4.6, "x": 1.02, "y": 0.98, "curve": [4.656, 1.02, 4.711, 1, 4.656, 0.98, 4.711, 1]}, {"time": 4.7667, "curve": [4.822, 1, 4.878, 1.02, 4.822, 1, 4.878, 0.98]}, {"time": 4.9333, "x": 1.02, "y": 0.98, "curve": [4.989, 1.02, 5.044, 1, 4.989, 0.98, 5.044, 1]}, {"time": 5.1}]}, "mouth": {"rotate": [{"curve": "stepped"}, {"time": 0.5333, "curve": [0.578, 0, 0.622, -1.29]}, {"time": 0.6667, "value": -1.29, "curve": [0.733, -1.29, 0.8, 2.51]}, {"time": 0.8667, "value": 2.51, "curve": "stepped"}, {"time": 1.8667, "value": 2.51, "curve": [1.911, 2.51, 1.956, 3.87]}, {"time": 2, "value": 3.87, "curve": [2.067, 3.87, 2.133, -2.61]}, {"time": 2.2, "value": -2.61, "curve": "stepped"}, {"time": 3.3333, "value": -2.61, "curve": [3.444, -2.61, 3.556, 0]}, {"time": 3.6667}], "translate": [{"x": -0.48, "curve": [0.022, -0.21, 0.044, 0, 0.022, 0, 0.044, 0]}, {"time": 0.0667, "curve": [0.167, 0, 0.267, -3.29, 0.167, 0, 0.267, -0.03]}, {"time": 0.3667, "x": -3.29, "y": -0.03, "curve": "stepped"}, {"time": 0.4333, "x": -3.29, "y": -0.03, "curve": [0.5, -3.29, 0.567, -1.31, 0.5, -0.03, 0.567, -0.01]}, {"time": 0.6333, "x": -0.48, "curve": [0.656, -0.21, 0.678, 0, 0.656, 0, 0.678, 0]}, {"time": 0.7, "curve": [0.8, 0, 0.9, -3.29, 0.8, 0, 0.9, -0.03]}, {"time": 1, "x": -3.29, "y": -0.03, "curve": "stepped"}, {"time": 1.0667, "x": -3.29, "y": -0.03, "curve": [1.133, -3.29, 1.2, -1.31, 1.133, -0.03, 1.2, -0.01]}, {"time": 1.2667, "x": -0.48, "curve": [1.289, -0.21, 1.311, 0, 1.289, 0, 1.311, 0]}, {"time": 1.3333, "curve": [1.433, 0, 1.533, -3.29, 1.433, 0, 1.533, -0.03]}, {"time": 1.6333, "x": -3.29, "y": -0.03, "curve": "stepped"}, {"time": 1.7, "x": -3.29, "y": -0.03, "curve": [1.767, -3.29, 1.833, -1.31, 1.767, -0.03, 1.833, -0.01]}, {"time": 1.9, "x": -0.48, "curve": [1.922, -0.21, 1.944, 0, 1.922, 0, 1.944, 0]}, {"time": 1.9667, "curve": [2.067, 0, 2.167, -3.29, 2.067, 0, 2.167, -0.03]}, {"time": 2.2667, "x": -3.29, "y": -0.03, "curve": "stepped"}, {"time": 2.3333, "x": -3.29, "y": -0.03, "curve": [2.4, -3.29, 2.467, -1.31, 2.4, -0.03, 2.467, -0.01]}, {"time": 2.5333, "x": -0.48, "curve": [2.556, -0.21, 2.578, 0, 2.556, 0, 2.578, 0]}, {"time": 2.6, "curve": [2.7, 0, 2.8, -3.29, 2.7, 0, 2.8, -0.03]}, {"time": 2.9, "x": -3.29, "y": -0.03, "curve": "stepped"}, {"time": 2.9667, "x": -3.29, "y": -0.03, "curve": [3.033, -3.29, 3.1, -1.31, 3.033, -0.03, 3.1, -0.01]}, {"time": 3.1667, "x": -0.48, "curve": [3.189, -0.21, 3.211, 0, 3.189, 0, 3.211, 0]}, {"time": 3.2333, "curve": [3.333, 0, 3.433, -3.29, 3.333, 0, 3.433, -0.03]}, {"time": 3.5333, "x": -3.29, "y": -0.03, "curve": "stepped"}, {"time": 3.6, "x": -3.29, "y": -0.03, "curve": [3.667, -3.29, 3.733, -1.31, 3.667, -0.03, 3.733, -0.01]}, {"time": 3.8, "x": -0.48, "curve": [3.822, -0.21, 3.844, 0, 3.822, 0, 3.844, 0]}, {"time": 3.8667, "curve": [3.967, 0, 4.067, -3.29, 3.967, 0, 4.067, -0.03]}, {"time": 4.1667, "x": -3.29, "y": -0.03, "curve": "stepped"}, {"time": 4.2333, "x": -3.29, "y": -0.03, "curve": [4.3, -3.29, 4.367, -1.31, 4.3, -0.03, 4.367, -0.01]}, {"time": 4.4333, "x": -0.48, "curve": [4.456, -0.21, 4.478, 0, 4.456, 0, 4.478, 0]}, {"time": 4.5, "curve": [4.6, 0, 4.7, -3.29, 4.6, 0, 4.7, -0.03]}, {"time": 4.8, "x": -3.29, "y": -0.03, "curve": "stepped"}, {"time": 4.8667, "x": -3.29, "y": -0.03, "curve": [4.933, -3.29, 5, -1.31, 4.933, -0.03, 5, -0.01]}, {"time": 5.0667, "x": -0.48}]}, "legR_ik": {"translate": [{"curve": [0.056, 0, 0.111, 6.55, 0.056, 0, 0.111, 7.93]}, {"time": 0.1667, "x": 8.93, "y": 7.93, "curve": [0.222, 11.31, 0.278, 14.28, 0.222, 7.93, 0.278, 0]}, {"time": 0.3333, "x": 14.28, "curve": [0.433, 14.28, 0.533, 0, 0.433, 0, 0.533, 0]}, {"time": 0.6333, "curve": [0.689, 0, 0.744, 6.55, 0.689, 0, 0.744, 7.93]}, {"time": 0.8, "x": 8.93, "y": 7.93, "curve": [0.856, 11.31, 0.911, 14.28, 0.856, 7.93, 0.911, 0]}, {"time": 0.9667, "x": 14.28, "curve": [1.067, 14.28, 1.167, 0, 1.067, 0, 1.167, 0]}, {"time": 1.2667, "curve": [1.322, 0, 1.378, 6.55, 1.322, 0, 1.378, 7.93]}, {"time": 1.4333, "x": 8.93, "y": 7.93, "curve": [1.489, 11.31, 1.544, 14.28, 1.489, 7.93, 1.544, 0]}, {"time": 1.6, "x": 14.28, "curve": [1.7, 14.28, 1.8, 0, 1.7, 0, 1.8, 0]}, {"time": 1.9, "curve": [1.956, 0, 2.011, 6.55, 1.956, 0, 2.011, 7.93]}, {"time": 2.0667, "x": 8.93, "y": 7.93, "curve": [2.122, 11.31, 2.178, 14.28, 2.122, 7.93, 2.178, 0]}, {"time": 2.2333, "x": 14.28, "curve": [2.333, 14.28, 2.433, 0, 2.333, 0, 2.433, 0]}, {"time": 2.5333, "curve": "stepped"}, {"time": 2.5667, "curve": [2.622, 0, 2.678, 6.55, 2.622, 0, 2.678, 7.93]}, {"time": 2.7333, "x": 8.93, "y": 7.93, "curve": [2.789, 11.31, 2.844, 14.28, 2.789, 7.93, 2.844, 0]}, {"time": 2.9, "x": 14.28, "curve": [3, 14.28, 3.1, 0, 3, 0, 3.1, 0]}, {"time": 3.2, "curve": [3.256, 0, 3.311, 6.55, 3.256, 0, 3.311, 7.93]}, {"time": 3.3667, "x": 8.93, "y": 7.93, "curve": [3.422, 11.31, 3.478, 14.28, 3.422, 7.93, 3.478, 0]}, {"time": 3.5333, "x": 14.28, "curve": [3.633, 14.28, 3.733, 0, 3.633, 0, 3.733, 0]}, {"time": 3.8333, "curve": [3.889, 0, 3.944, 6.55, 3.889, 0, 3.944, 7.93]}, {"time": 4, "x": 8.93, "y": 7.93, "curve": [4.056, 11.31, 4.111, 14.28, 4.056, 7.93, 4.111, 0]}, {"time": 4.1667, "x": 14.28, "curve": [4.267, 14.28, 4.367, 0, 4.267, 0, 4.367, 0]}, {"time": 4.4667, "curve": [4.522, 0, 4.578, 6.55, 4.522, 0, 4.578, 7.93]}, {"time": 4.6333, "x": 8.93, "y": 7.93, "curve": [4.689, 11.31, 4.744, 14.28, 4.689, 7.93, 4.744, 0]}, {"time": 4.8, "x": 14.28, "curve": [4.9, 14.28, 5, 0, 4.9, 0, 5, 0]}, {"time": 5.1}]}, "legL_ik": {"translate": [{"curve": [0.111, 0, 0.222, -12.69, 0.111, 0, 0.222, 0]}, {"time": 0.3333, "x": -12.69, "curve": [0.378, -12.69, 0.422, -8.03, 0.378, 0, 0.422, 4.76]}, {"time": 0.4667, "x": -6.15, "y": 4.76, "curve": [0.522, -3.8, 0.578, 0, 0.522, 4.76, 0.578, 0]}, {"time": 0.6333, "curve": [0.744, 0, 0.856, -12.69, 0.744, 0, 0.856, 0]}, {"time": 0.9667, "x": -12.69, "curve": [1.011, -12.69, 1.056, -8.03, 1.011, 0, 1.056, 4.76]}, {"time": 1.1, "x": -6.15, "y": 4.76, "curve": [1.156, -3.8, 1.211, 0, 1.156, 4.76, 1.211, 0]}, {"time": 1.2667, "curve": [1.378, 0, 1.489, -12.69, 1.378, 0, 1.489, 0]}, {"time": 1.6, "x": -12.69, "curve": [1.644, -12.69, 1.689, -8.03, 1.644, 0, 1.689, 4.76]}, {"time": 1.7333, "x": -6.15, "y": 4.76, "curve": [1.789, -3.8, 1.844, 0, 1.789, 4.76, 1.844, 0]}, {"time": 1.9, "curve": [2.011, 0, 2.122, -12.69, 2.011, 0, 2.122, 0]}, {"time": 2.2333, "x": -12.69, "curve": [2.278, -12.69, 2.322, -8.03, 2.278, 0, 2.322, 4.76]}, {"time": 2.3667, "x": -6.15, "y": 4.76, "curve": [2.422, -3.8, 2.478, 0, 2.422, 4.76, 2.478, 0]}, {"time": 2.5333, "curve": "stepped"}, {"time": 2.5667, "curve": [2.678, 0, 2.789, -12.69, 2.678, 0, 2.789, 0]}, {"time": 2.9, "x": -12.69, "curve": [2.944, -12.69, 2.989, -8.03, 2.944, 0, 2.989, 4.76]}, {"time": 3.0333, "x": -6.15, "y": 4.76, "curve": [3.089, -3.8, 3.144, 0, 3.089, 4.76, 3.144, 0]}, {"time": 3.2, "curve": [3.311, 0, 3.422, -12.69, 3.311, 0, 3.422, 0]}, {"time": 3.5333, "x": -12.69, "curve": [3.578, -12.69, 3.622, -8.03, 3.578, 0, 3.622, 4.76]}, {"time": 3.6667, "x": -6.15, "y": 4.76, "curve": [3.722, -3.8, 3.778, 0, 3.722, 4.76, 3.778, 0]}, {"time": 3.8333, "curve": [3.944, 0, 4.056, -12.69, 3.944, 0, 4.056, 0]}, {"time": 4.1667, "x": -12.69, "curve": [4.211, -12.69, 4.256, -8.03, 4.211, 0, 4.256, 4.76]}, {"time": 4.3, "x": -6.15, "y": 4.76, "curve": [4.356, -3.8, 4.411, 0, 4.356, 4.76, 4.411, 0]}, {"time": 4.4667, "curve": [4.578, 0, 4.689, -12.69, 4.578, 0, 4.689, 0]}, {"time": 4.8, "x": -12.69, "curve": [4.844, -12.69, 4.889, -8.03, 4.844, 0, 4.889, 4.76]}, {"time": 4.9333, "x": -6.15, "y": 4.76, "curve": [4.989, -3.8, 5.044, 0, 4.989, 4.76, 5.044, 0]}, {"time": 5.1}]}, "legR": {"translate": [{"x": 0.16, "y": 4.76, "curve": [0.1, 0.16, 0.2, 7.8, 0.1, 4.76, 0.2, -4.6]}, {"time": 0.3, "x": 7.8, "y": -4.6, "curve": "stepped"}, {"time": 0.3333, "x": 7.8, "y": -4.6, "curve": [0.433, 7.8, 0.533, 0.16, 0.433, -4.6, 0.533, 4.76]}, {"time": 0.6333, "x": 0.16, "y": 4.76, "curve": [0.733, 0.16, 0.833, 7.8, 0.733, 4.76, 0.833, -4.6]}, {"time": 0.9333, "x": 7.8, "y": -4.6, "curve": "stepped"}, {"time": 0.9667, "x": 7.8, "y": -4.6, "curve": [1.067, 7.8, 1.167, 0.16, 1.067, -4.6, 1.167, 4.76]}, {"time": 1.2667, "x": 0.16, "y": 4.76, "curve": [1.367, 0.16, 1.467, 7.8, 1.367, 4.76, 1.467, -4.6]}, {"time": 1.5667, "x": 7.8, "y": -4.6, "curve": "stepped"}, {"time": 1.6, "x": 7.8, "y": -4.6, "curve": [1.7, 7.8, 1.8, 0.16, 1.7, -4.6, 1.8, 4.76]}, {"time": 1.9, "x": 0.16, "y": 4.76, "curve": [2, 0.16, 2.1, 7.8, 2, 4.76, 2.1, -4.6]}, {"time": 2.2, "x": 7.8, "y": -4.6, "curve": "stepped"}, {"time": 2.2333, "x": 7.8, "y": -4.6, "curve": [2.333, 7.8, 2.433, 0.16, 2.333, -4.6, 2.433, 4.76]}, {"time": 2.5333, "x": 0.16, "y": 4.76, "curve": "stepped"}, {"time": 2.5667, "x": 0.16, "y": 4.76, "curve": [2.667, 0.16, 2.767, 7.8, 2.667, 4.76, 2.767, -4.6]}, {"time": 2.8667, "x": 7.8, "y": -4.6, "curve": "stepped"}, {"time": 2.9, "x": 7.8, "y": -4.6, "curve": [3, 7.8, 3.1, 0.16, 3, -4.6, 3.1, 4.76]}, {"time": 3.2, "x": 0.16, "y": 4.76, "curve": [3.3, 0.16, 3.4, 7.8, 3.3, 4.76, 3.4, -4.6]}, {"time": 3.5, "x": 7.8, "y": -4.6, "curve": "stepped"}, {"time": 3.5333, "x": 7.8, "y": -4.6, "curve": [3.633, 7.8, 3.733, 0.16, 3.633, -4.6, 3.733, 4.76]}, {"time": 3.8333, "x": 0.16, "y": 4.76, "curve": [3.933, 0.16, 4.033, 7.8, 3.933, 4.76, 4.033, -4.6]}, {"time": 4.1333, "x": 7.8, "y": -4.6, "curve": "stepped"}, {"time": 4.1667, "x": 7.8, "y": -4.6, "curve": [4.267, 7.8, 4.367, 0.16, 4.267, -4.6, 4.367, 4.76]}, {"time": 4.4667, "x": 0.16, "y": 4.76, "curve": [4.567, 0.16, 4.667, 7.8, 4.567, 4.76, 4.667, -4.6]}, {"time": 4.7667, "x": 7.8, "y": -4.6, "curve": "stepped"}, {"time": 4.8, "x": 7.8, "y": -4.6, "curve": [4.9, 7.8, 5, 0.16, 4.9, -4.6, 5, 4.76]}, {"time": 5.1, "x": 0.16, "y": 4.76}]}, "legL": {"translate": [{"x": -0.08, "y": -4.75, "curve": [0.1, -0.08, 0.2, 7.79, 0.1, -4.75, 0.2, 3.19]}, {"time": 0.3, "x": 7.79, "y": 3.19, "curve": "stepped"}, {"time": 0.3333, "x": 7.79, "y": 3.19, "curve": [0.433, 7.79, 0.533, -0.08, 0.433, 3.19, 0.533, -4.75]}, {"time": 0.6333, "x": -0.08, "y": -4.75, "curve": [0.733, -0.08, 0.833, 7.79, 0.733, -4.75, 0.833, 3.19]}, {"time": 0.9333, "x": 7.79, "y": 3.19, "curve": "stepped"}, {"time": 0.9667, "x": 7.79, "y": 3.19, "curve": [1.067, 7.79, 1.167, -0.08, 1.067, 3.19, 1.167, -4.75]}, {"time": 1.2667, "x": -0.08, "y": -4.75, "curve": [1.367, -0.08, 1.467, 7.79, 1.367, -4.75, 1.467, 3.19]}, {"time": 1.5667, "x": 7.79, "y": 3.19, "curve": "stepped"}, {"time": 1.6, "x": 7.79, "y": 3.19, "curve": [1.7, 7.79, 1.8, -0.08, 1.7, 3.19, 1.8, -4.75]}, {"time": 1.9, "x": -0.08, "y": -4.75, "curve": [2, -0.08, 2.1, 7.79, 2, -4.75, 2.1, 3.19]}, {"time": 2.2, "x": 7.79, "y": 3.19, "curve": "stepped"}, {"time": 2.2333, "x": 7.79, "y": 3.19, "curve": [2.333, 7.79, 2.433, -0.08, 2.333, 3.19, 2.433, -4.75]}, {"time": 2.5333, "x": -0.08, "y": -4.75, "curve": "stepped"}, {"time": 2.5667, "x": -0.08, "y": -4.75, "curve": [2.667, -0.08, 2.767, 7.79, 2.667, -4.75, 2.767, 3.19]}, {"time": 2.8667, "x": 7.79, "y": 3.19, "curve": "stepped"}, {"time": 2.9, "x": 7.79, "y": 3.19, "curve": [3, 7.79, 3.1, -0.08, 3, 3.19, 3.1, -4.75]}, {"time": 3.2, "x": -0.08, "y": -4.75, "curve": [3.3, -0.08, 3.4, 7.79, 3.3, -4.75, 3.4, 3.19]}, {"time": 3.5, "x": 7.79, "y": 3.19, "curve": "stepped"}, {"time": 3.5333, "x": 7.79, "y": 3.19, "curve": [3.633, 7.79, 3.733, -0.08, 3.633, 3.19, 3.733, -4.75]}, {"time": 3.8333, "x": -0.08, "y": -4.75, "curve": [3.933, -0.08, 4.033, 7.79, 3.933, -4.75, 4.033, 3.19]}, {"time": 4.1333, "x": 7.79, "y": 3.19, "curve": "stepped"}, {"time": 4.1667, "x": 7.79, "y": 3.19, "curve": [4.267, 7.79, 4.367, -0.08, 4.267, 3.19, 4.367, -4.75]}, {"time": 4.4667, "x": -0.08, "y": -4.75, "curve": [4.567, -0.08, 4.667, 7.79, 4.567, -4.75, 4.667, 3.19]}, {"time": 4.7667, "x": 7.79, "y": 3.19, "curve": "stepped"}, {"time": 4.8, "x": 7.79, "y": 3.19, "curve": [4.9, 7.79, 5, -0.08, 4.9, 3.19, 5, -4.75]}, {"time": 5.1, "x": -0.08, "y": -4.75}]}, "face": {"translatex": [{"curve": "stepped"}, {"time": 0.5333, "curve": [0.567, 0.01, 0.6, 0.26]}, {"time": 0.6333, "value": 0.26, "curve": [0.667, 0.26, 0.7, -1.63]}, {"time": 0.7333, "value": -1.63, "curve": [0.822, -1.63, 0.911, 1.02]}, {"time": 1, "value": 1.02, "curve": "stepped"}, {"time": 1.8667, "value": 1.02, "curve": "stepped"}, {"time": 2, "value": 1.02, "curve": "stepped"}, {"time": 2.2, "value": 1.02, "curve": "stepped"}, {"time": 3.3333, "value": 1.02, "curve": "stepped"}, {"time": 3.4333, "value": 1.02, "curve": [3.478, 1.02, 3.522, -1.48]}, {"time": 3.5667, "value": -1.48, "curve": [3.6, -1.48, 3.633, -0.49]}, {"time": 3.6667}], "translatey": [{"curve": "stepped"}, {"time": 0.5333, "curve": [0.567, 0.19, 0.6, 0.56]}, {"time": 0.6333, "value": 0.56, "curve": [0.667, 0.56, 0.7, -1.91]}, {"time": 0.7333, "value": -2.91, "curve": [0.822, -5.58, 0.911, -8.03]}, {"time": 1, "value": -8.03, "curve": "stepped"}, {"time": 1.8667, "value": -8.03, "curve": [1.911, -8.54, 1.956, -9.57]}, {"time": 2, "value": -9.57, "curve": [2.067, -9.57, 2.133, -3.18]}, {"time": 2.2, "value": -1.94, "curve": "stepped"}, {"time": 3.3333, "value": -1.94, "curve": [3.367, -0.37, 3.4, 2.77]}, {"time": 3.4333, "value": 2.77, "curve": [3.511, 2.77, 3.589, 0.92]}, {"time": 3.6667}]}, "pupil": {"translate": [{"curve": "stepped"}, {"time": 0.6667, "curve": [0.689, 0, 0.678, -0.29, 0.689, 0, 0.678, -9.63]}, {"time": 0.7, "x": -0.29, "y": -9.63, "curve": "stepped"}, {"time": 1.9667, "x": -0.29, "y": -9.63, "curve": [1.989, -0.29, 1.978, -0.29, 1.989, -9.63, 1.978, 4.47]}, {"time": 2, "x": -0.29, "y": 4.47, "curve": "stepped"}, {"time": 3.3333, "x": -0.29, "y": 4.47}, {"time": 3.3667}]}, "brow": {"translate": [{"curve": "stepped"}, {"time": 0.6667, "curve": "stepped"}, {"time": 3.2333}]}, "eye2": {"translate": [{"curve": "stepped"}, {"time": 0.6667, "curve": "stepped"}, {"time": 3.2333}]}, "brow_m": {"translate": [{"curve": "stepped"}, {"time": 0.5667, "curve": [0.6, 0, 0.633, -2.87, 0.6, 0, 0.633, 0.07]}, {"time": 0.6667, "x": -2.87, "y": 0.07, "curve": [0.722, -2.87, 0.778, 0, 0.722, 0.07, 0.778, 0]}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.9, "curve": [1.933, 0, 1.967, -2.87, 1.933, 0, 1.967, 0.07]}, {"time": 2, "x": -2.87, "y": 0.07, "curve": [2.056, -2.87, 2.111, 0, 2.056, 0.07, 2.111, 0]}, {"time": 2.1667, "curve": "stepped"}, {"time": 3.2333, "curve": [3.267, 0, 3.3, -2.87, 3.267, 0, 3.3, 0.07]}, {"time": 3.3333, "x": -2.87, "y": 0.07, "curve": [3.389, -2.87, 3.444, 0, 3.389, 0.07, 3.444, 0]}, {"time": 3.5}]}, "body_cntrl": {"rotate": [{"curve": "stepped"}, {"time": 0.5333, "curve": [0.567, 0, 0.6, -0.72]}, {"time": 0.6333, "value": -0.72, "curve": [0.689, -0.72, 0.744, 2.13]}, {"time": 0.8, "value": 2.13, "curve": "stepped"}, {"time": 1.8667, "value": 2.13, "curve": [1.911, 2.13, 1.956, 2.54]}, {"time": 2, "value": 2.54, "curve": [2.044, 2.54, 2.089, -1.61]}, {"time": 2.1333, "value": -1.61, "curve": "stepped"}, {"time": 3.4333, "value": -1.61, "curve": [3.489, -1.61, 3.544, 0]}, {"time": 3.6}]}, "eye_up_closed": {"translate": [{"x": 35.51, "curve": "stepped"}, {"time": 0.5333, "x": 35.51, "curve": [0.578, 23.68, 0.622, -4.47, 0.578, 0, 0.622, 0]}, {"time": 0.6667, "x": -4.47, "curve": "stepped"}, {"time": 0.7, "x": -4.47, "curve": [0.711, -4.47, 0.778, 23.68, 0.711, 0, 0.778, 0]}, {"time": 0.8333, "x": 35.51, "curve": "stepped"}, {"time": 1.8333, "x": 35.51, "curve": [1.878, 23.68, 1.922, -4.47, 1.878, 0, 1.922, 0]}, {"time": 1.9667, "x": -4.47, "curve": "stepped"}, {"time": 2, "x": -4.47, "curve": [2.011, -4.47, 2.078, 23.68, 2.011, 0, 2.078, 0]}, {"time": 2.1333, "x": 35.51, "curve": "stepped"}, {"time": 3.2, "x": 35.51, "curve": [3.244, 23.68, 3.289, -4.47, 3.244, 0, 3.289, 0]}, {"time": 3.3333, "x": -4.47, "curve": "stepped"}, {"time": 3.3667, "x": -4.47, "curve": [3.378, -4.47, 3.444, 23.68, 3.378, 0, 3.444, 0]}, {"time": 3.5, "x": 35.51}]}}}, "t1_Reaction": {"slots": {"eyelid_u": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.1333, "color": "ffffff00", "curve": [0.144, 1, 0.156, 1, 0.144, 1, 0.156, 1, 0.144, 1, 0.156, 1, 0.144, 0, 0.156, 1]}, {"time": 0.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.4, "color": "ffffffff", "curve": [0.411, 1, 0.422, 1, 0.411, 1, 0.422, 1, 0.411, 1, 0.422, 1, 0.411, 1, 0.422, 0]}, {"time": 0.4333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.0333, "color": "ffffff00", "curve": [2.044, 1, 2.056, 1, 2.044, 1, 2.056, 1, 2.044, 1, 2.056, 1, 2.044, 0, 2.056, 1]}, {"time": 2.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.3, "color": "ffffffff", "curve": [2.311, 1, 2.322, 1, 2.311, 1, 2.322, 1, 2.311, 1, 2.322, 1, 2.311, 1, 2.322, 0]}, {"time": 2.3333, "color": "ffffff00", "curve": "stepped"}, {"time": 3.9333, "color": "ffffff00", "curve": [3.944, 1, 3.956, 1, 3.944, 1, 3.956, 1, 3.944, 1, 3.956, 1, 3.944, 0, 3.956, 1]}, {"time": 3.9667, "color": "ffffffff", "curve": "stepped"}, {"time": 4.2, "color": "ffffffff", "curve": [4.211, 1, 4.222, 1, 4.211, 1, 4.222, 1, 4.211, 1, 4.222, 1, 4.211, 1, 4.222, 0]}, {"time": 4.2333, "color": "ffffff00"}]}}, "bones": {"body": {"rotate": [{"value": 2.44, "curve": [0.023, 2.66, 0.045, 2.75]}, {"time": 0.0667, "value": 2.58, "curve": [0.178, 2.58, 0.289, -0.87]}, {"time": 0.4, "value": -0.87, "curve": [0.5, -0.87, 0.6, 3.35]}, {"time": 0.7, "value": 2.58, "curve": [0.811, 1.73, 0.922, -0.87]}, {"time": 1.0333, "value": -0.87, "curve": [1.111, -0.87, 1.189, 1.7]}, {"time": 1.2667, "value": 2.44, "curve": [1.289, 2.66, 1.311, 2.75]}, {"time": 1.3333, "value": 2.58, "curve": [1.444, 2.58, 1.556, -0.87]}, {"time": 1.6667, "value": -0.87, "curve": [1.767, -0.87, 1.867, 3.35]}, {"time": 1.9667, "value": 2.58, "curve": [2.078, 1.73, 2.189, -0.87]}, {"time": 2.3, "value": -0.87, "curve": [2.378, -0.87, 2.456, 1.68]}, {"time": 2.5333, "value": 2.44, "curve": "stepped"}, {"time": 2.5667, "value": 2.44, "curve": [2.589, 2.66, 2.611, 2.75]}, {"time": 2.6333, "value": 2.58, "curve": [2.744, 2.58, 2.856, -0.87]}, {"time": 2.9667, "value": -0.87, "curve": [3.067, -0.87, 3.167, 3.35]}, {"time": 3.2667, "value": 2.58, "curve": [3.378, 1.73, 3.489, -0.87]}, {"time": 3.6, "value": -0.87, "curve": [3.678, -0.87, 3.756, 1.7]}, {"time": 3.8333, "value": 2.44, "curve": [3.856, 2.66, 3.878, 2.75]}, {"time": 3.9, "value": 2.58, "curve": [4.011, 2.58, 4.122, -0.87]}, {"time": 4.2333, "value": -0.87, "curve": [4.333, -0.87, 4.433, 3.35]}, {"time": 4.5333, "value": 2.58, "curve": [4.644, 1.73, 4.756, -0.87]}, {"time": 4.8667, "value": -0.87, "curve": [4.945, -0.87, 5.023, 1.68]}, {"time": 5.1, "value": 2.44}], "translate": [{"x": 3.5, "y": 0.15, "curve": [0.1, 3.5, 0.2, 3.5, 0.1, 0.15, 0.2, 7.41]}, {"time": 0.3, "x": 3.5, "y": 7.41, "curve": "stepped"}, {"time": 0.3333, "x": 3.5, "y": 7.41, "curve": [0.433, 3.5, 0.533, 3.5, 0.433, 7.41, 0.533, 0.15]}, {"time": 0.6333, "x": 3.5, "y": 0.15, "curve": [0.733, 3.5, 0.833, 3.5, 0.733, 0.15, 0.833, 7.41]}, {"time": 0.9333, "x": 3.5, "y": 7.41, "curve": "stepped"}, {"time": 0.9667, "x": 3.5, "y": 7.41, "curve": [1.067, 3.5, 1.167, 3.5, 1.067, 7.41, 1.167, 0.15]}, {"time": 1.2667, "x": 3.5, "y": 0.15, "curve": [1.367, 3.5, 1.467, 3.5, 1.367, 0.15, 1.467, 7.41]}, {"time": 1.5667, "x": 3.5, "y": 7.41, "curve": "stepped"}, {"time": 1.6, "x": 3.5, "y": 7.41, "curve": [1.7, 3.5, 1.8, 3.5, 1.7, 7.41, 1.8, 0.15]}, {"time": 1.9, "x": 3.5, "y": 0.15, "curve": [2, 3.5, 2.1, 3.5, 2, 0.15, 2.1, 7.41]}, {"time": 2.2, "x": 3.5, "y": 7.41, "curve": "stepped"}, {"time": 2.2333, "x": 3.5, "y": 7.41, "curve": [2.333, 3.5, 2.433, 3.5, 2.333, 7.41, 2.433, 0.15]}, {"time": 2.5333, "x": 3.5, "y": 0.15, "curve": "stepped"}, {"time": 2.5667, "x": 3.5, "y": 0.15, "curve": [2.667, 3.5, 2.767, 3.5, 2.667, 0.15, 2.767, 7.41]}, {"time": 2.8667, "x": 3.5, "y": 7.41, "curve": "stepped"}, {"time": 2.9, "x": 3.5, "y": 7.41, "curve": [3, 3.5, 3.1, 3.5, 3, 7.41, 3.1, 0.15]}, {"time": 3.2, "x": 3.5, "y": 0.15, "curve": [3.3, 3.5, 3.4, 3.5, 3.3, 0.15, 3.4, 7.41]}, {"time": 3.5, "x": 3.5, "y": 7.41, "curve": "stepped"}, {"time": 3.5333, "x": 3.5, "y": 7.41, "curve": [3.633, 3.5, 3.733, 3.5, 3.633, 7.41, 3.733, 0.15]}, {"time": 3.8333, "x": 3.5, "y": 0.15, "curve": [3.933, 3.5, 4.033, 3.5, 3.933, 0.15, 4.033, 7.41]}, {"time": 4.1333, "x": 3.5, "y": 7.41, "curve": "stepped"}, {"time": 4.1667, "x": 3.5, "y": 7.41, "curve": [4.267, 3.5, 4.367, 3.5, 4.267, 7.41, 4.367, 0.15]}, {"time": 4.4667, "x": 3.5, "y": 0.15, "curve": [4.567, 3.5, 4.667, 3.5, 4.567, 0.15, 4.667, 7.41]}, {"time": 4.7667, "x": 3.5, "y": 7.41, "curve": "stepped"}, {"time": 4.8, "x": 3.5, "y": 7.41, "curve": [4.9, 3.5, 5, 3.5, 4.9, 7.41, 5, 0.15]}, {"time": 5.1, "x": 3.5, "y": 0.15}], "scale": [{"curve": [0.056, 1, 0.111, 1.02, 0.056, 1, 0.111, 0.98]}, {"time": 0.1667, "x": 1.02, "y": 0.98, "curve": [0.211, 1.02, 0.256, 1, 0.211, 0.98, 0.256, 1]}, {"time": 0.3, "curve": [0.356, 1, 0.411, 1.02, 0.356, 1, 0.411, 0.98]}, {"time": 0.4667, "x": 1.02, "y": 0.98, "curve": [0.522, 1.02, 0.578, 1, 0.522, 0.98, 0.578, 1]}, {"time": 0.6333, "curve": [0.678, 1, 0.722, 1.02, 0.678, 1, 0.722, 0.98]}, {"time": 0.7667, "x": 1.02, "y": 0.98, "curve": [0.822, 1.02, 0.878, 1, 0.822, 0.98, 0.878, 1]}, {"time": 0.9333, "curve": [0.989, 1, 1.044, 1.02, 0.989, 1, 1.044, 0.98]}, {"time": 1.1, "x": 1.02, "y": 0.98, "curve": [1.156, 1.02, 1.211, 1, 1.156, 0.98, 1.211, 1]}, {"time": 1.2667, "curve": [1.322, 1, 1.378, 1.02, 1.322, 1, 1.378, 0.98]}, {"time": 1.4333, "x": 1.02, "y": 0.98, "curve": [1.478, 1.02, 1.522, 1, 1.478, 0.98, 1.522, 1]}, {"time": 1.5667, "curve": [1.622, 1, 1.678, 1.02, 1.622, 1, 1.678, 0.98]}, {"time": 1.7333, "x": 1.02, "y": 0.98, "curve": [1.789, 1.02, 1.844, 1, 1.789, 0.98, 1.844, 1]}, {"time": 1.9, "curve": [1.944, 1, 1.989, 1.02, 1.944, 1, 1.989, 0.98]}, {"time": 2.0333, "x": 1.02, "y": 0.98, "curve": [2.089, 1.02, 2.144, 1, 2.089, 0.98, 2.144, 1]}, {"time": 2.2, "curve": [2.256, 1, 2.311, 1.02, 2.256, 1, 2.311, 0.98]}, {"time": 2.3667, "x": 1.02, "y": 0.98, "curve": [2.422, 1.02, 2.478, 1, 2.422, 0.98, 2.478, 1]}, {"time": 2.5333, "curve": "stepped"}, {"time": 2.5667, "curve": [2.622, 1, 2.678, 1.02, 2.622, 1, 2.678, 0.98]}, {"time": 2.7333, "x": 1.02, "y": 0.98, "curve": [2.778, 1.02, 2.822, 1, 2.778, 0.98, 2.822, 1]}, {"time": 2.8667, "curve": [2.922, 1, 2.978, 1.02, 2.922, 1, 2.978, 0.98]}, {"time": 3.0333, "x": 1.02, "y": 0.98, "curve": [3.089, 1.02, 3.144, 1, 3.089, 0.98, 3.144, 1]}, {"time": 3.2, "curve": [3.244, 1, 3.289, 1.02, 3.244, 1, 3.289, 0.98]}, {"time": 3.3333, "x": 1.02, "y": 0.98, "curve": [3.389, 1.02, 3.444, 1, 3.389, 0.98, 3.444, 1]}, {"time": 3.5, "curve": [3.556, 1, 3.611, 1.02, 3.556, 1, 3.611, 0.98]}, {"time": 3.6667, "x": 1.02, "y": 0.98, "curve": [3.722, 1.02, 3.778, 1, 3.722, 0.98, 3.778, 1]}, {"time": 3.8333, "curve": [3.889, 1, 3.944, 1.02, 3.889, 1, 3.944, 0.98]}, {"time": 4, "x": 1.02, "y": 0.98, "curve": [4.044, 1.02, 4.089, 1, 4.044, 0.98, 4.089, 1]}, {"time": 4.1333, "curve": [4.189, 1, 4.244, 1.02, 4.189, 1, 4.244, 0.98]}, {"time": 4.3, "x": 1.02, "y": 0.98, "curve": [4.356, 1.02, 4.411, 1, 4.356, 0.98, 4.411, 1]}, {"time": 4.4667, "curve": [4.511, 1, 4.556, 1.02, 4.511, 1, 4.556, 0.98]}, {"time": 4.6, "x": 1.02, "y": 0.98, "curve": [4.656, 1.02, 4.711, 1, 4.656, 0.98, 4.711, 1]}, {"time": 4.7667, "curve": [4.822, 1, 4.878, 1.02, 4.822, 1, 4.878, 0.98]}, {"time": 4.9333, "x": 1.02, "y": 0.98, "curve": [4.989, 1.02, 5.044, 1, 4.989, 0.98, 5.044, 1]}, {"time": 5.1}]}, "mouth": {"rotate": [{"curve": "stepped"}, {"time": 0.5333, "curve": [0.578, 0, 0.622, -1.29]}, {"time": 0.6667, "value": -1.29, "curve": [0.733, -1.29, 0.8, 2.51]}, {"time": 0.8667, "value": 2.51, "curve": "stepped"}, {"time": 1.8667, "value": 2.51, "curve": [1.911, 2.51, 1.956, 3.87]}, {"time": 2, "value": 3.87, "curve": [2.067, 3.87, 2.133, -2.61]}, {"time": 2.2, "value": -2.61, "curve": "stepped"}, {"time": 3.3333, "value": -2.61, "curve": [3.444, -2.61, 3.556, 0]}, {"time": 3.6667}], "translate": [{"x": -2.96, "y": -0.28, "curve": [0.022, -2.68, 0.044, -2.47, 0.022, -0.27, 0.044, -0.27]}, {"time": 0.0667, "x": -2.47, "y": -0.27, "curve": [0.167, -2.47, 0.267, -5.77, 0.167, -0.27, 0.267, -0.3]}, {"time": 0.3667, "x": -5.77, "y": -0.3, "curve": "stepped"}, {"time": 0.4333, "x": -5.77, "y": -0.3, "curve": [0.5, -5.77, 0.567, -3.78, 0.5, -0.3, 0.567, -0.28]}, {"time": 0.6333, "x": -2.96, "y": -0.28, "curve": [0.656, -2.68, 0.678, -2.47, 0.656, -0.27, 0.678, -0.27]}, {"time": 0.7, "x": -2.47, "y": -0.27, "curve": [0.8, -2.47, 0.9, -5.77, 0.8, -0.27, 0.9, -0.3]}, {"time": 1, "x": -5.77, "y": -0.3, "curve": "stepped"}, {"time": 1.0667, "x": -5.77, "y": -0.3, "curve": [1.133, -5.77, 1.2, -3.78, 1.133, -0.3, 1.2, -0.28]}, {"time": 1.2667, "x": -2.96, "y": -0.28, "curve": [1.289, -2.68, 1.311, -2.47, 1.289, -0.27, 1.311, -0.27]}, {"time": 1.3333, "x": -2.47, "y": -0.27, "curve": [1.433, -2.47, 1.533, -5.77, 1.433, -0.27, 1.533, -0.3]}, {"time": 1.6333, "x": -5.77, "y": -0.3, "curve": "stepped"}, {"time": 1.7, "x": -5.77, "y": -0.3, "curve": [1.767, -5.77, 1.833, -3.78, 1.767, -0.3, 1.833, -0.28]}, {"time": 1.9, "x": -2.96, "y": -0.28, "curve": [1.922, -2.68, 1.944, -2.47, 1.922, -0.27, 1.944, -0.27]}, {"time": 1.9667, "x": -2.47, "y": -0.27, "curve": [2.067, -2.47, 2.167, -5.77, 2.067, -0.27, 2.167, -0.3]}, {"time": 2.2667, "x": -5.77, "y": -0.3, "curve": "stepped"}, {"time": 2.3333, "x": -5.77, "y": -0.3, "curve": [2.4, -5.77, 2.467, -3.78, 2.4, -0.3, 2.467, -0.28]}, {"time": 2.5333, "x": -2.96, "y": -0.28, "curve": [2.556, -2.68, 2.578, -2.47, 2.556, -0.27, 2.578, -0.27]}, {"time": 2.6, "x": -2.47, "y": -0.27, "curve": [2.7, -2.47, 2.8, -5.77, 2.7, -0.27, 2.8, -0.3]}, {"time": 2.9, "x": -5.77, "y": -0.3, "curve": "stepped"}, {"time": 2.9667, "x": -5.77, "y": -0.3, "curve": [3.033, -5.77, 3.1, -3.78, 3.033, -0.3, 3.1, -0.28]}, {"time": 3.1667, "x": -2.96, "y": -0.28, "curve": [3.189, -2.68, 3.211, -2.47, 3.189, -0.27, 3.211, -0.27]}, {"time": 3.2333, "x": -2.47, "y": -0.27, "curve": [3.333, -2.47, 3.433, -5.77, 3.333, -0.27, 3.433, -0.3]}, {"time": 3.5333, "x": -5.77, "y": -0.3, "curve": "stepped"}, {"time": 3.6, "x": -5.77, "y": -0.3, "curve": [3.667, -5.77, 3.733, -3.78, 3.667, -0.3, 3.733, -0.28]}, {"time": 3.8, "x": -2.96, "y": -0.28, "curve": [3.822, -2.68, 3.844, -2.47, 3.822, -0.27, 3.844, -0.27]}, {"time": 3.8667, "x": -2.47, "y": -0.27, "curve": [3.967, -2.47, 4.067, -5.77, 3.967, -0.27, 4.067, -0.3]}, {"time": 4.1667, "x": -5.77, "y": -0.3, "curve": "stepped"}, {"time": 4.2333, "x": -5.77, "y": -0.3, "curve": [4.3, -5.77, 4.367, -3.78, 4.3, -0.3, 4.367, -0.28]}, {"time": 4.4333, "x": -2.96, "y": -0.28, "curve": [4.456, -2.68, 4.478, -2.47, 4.456, -0.27, 4.478, -0.27]}, {"time": 4.5, "x": -2.47, "y": -0.27, "curve": [4.6, -2.47, 4.7, -5.77, 4.6, -0.27, 4.7, -0.3]}, {"time": 4.8, "x": -5.77, "y": -0.3, "curve": "stepped"}, {"time": 4.8667, "x": -5.77, "y": -0.3, "curve": [4.933, -5.77, 5, -3.78, 4.933, -0.3, 5, -0.28]}, {"time": 5.0667, "x": -2.96, "y": -0.28}]}, "legR_ik": {"translate": [{"curve": [0.056, 0, 0.111, 6.55, 0.056, 0, 0.111, 7.93]}, {"time": 0.1667, "x": 8.93, "y": 7.93, "curve": [0.222, 11.31, 0.278, 14.28, 0.222, 7.93, 0.278, 0]}, {"time": 0.3333, "x": 14.28, "curve": [0.433, 14.28, 0.533, 0, 0.433, 0, 0.533, 0]}, {"time": 0.6333, "curve": [0.689, 0, 0.744, 6.55, 0.689, 0, 0.744, 7.93]}, {"time": 0.8, "x": 8.93, "y": 7.93, "curve": [0.856, 11.31, 0.911, 14.28, 0.856, 7.93, 0.911, 0]}, {"time": 0.9667, "x": 14.28, "curve": [1.067, 14.28, 1.167, 0, 1.067, 0, 1.167, 0]}, {"time": 1.2667, "curve": [1.322, 0, 1.378, 6.55, 1.322, 0, 1.378, 7.93]}, {"time": 1.4333, "x": 8.93, "y": 7.93, "curve": [1.489, 11.31, 1.544, 14.28, 1.489, 7.93, 1.544, 0]}, {"time": 1.6, "x": 14.28, "curve": [1.7, 14.28, 1.8, 0, 1.7, 0, 1.8, 0]}, {"time": 1.9, "curve": [1.956, 0, 2.011, 6.55, 1.956, 0, 2.011, 7.93]}, {"time": 2.0667, "x": 8.93, "y": 7.93, "curve": [2.122, 11.31, 2.178, 14.28, 2.122, 7.93, 2.178, 0]}, {"time": 2.2333, "x": 14.28, "curve": [2.333, 14.28, 2.433, 0, 2.333, 0, 2.433, 0]}, {"time": 2.5333, "curve": "stepped"}, {"time": 2.5667, "curve": [2.622, 0, 2.678, 6.55, 2.622, 0, 2.678, 7.93]}, {"time": 2.7333, "x": 8.93, "y": 7.93, "curve": [2.789, 11.31, 2.844, 14.28, 2.789, 7.93, 2.844, 0]}, {"time": 2.9, "x": 14.28, "curve": [3, 14.28, 3.1, 0, 3, 0, 3.1, 0]}, {"time": 3.2, "curve": [3.256, 0, 3.311, 6.55, 3.256, 0, 3.311, 7.93]}, {"time": 3.3667, "x": 8.93, "y": 7.93, "curve": [3.422, 11.31, 3.478, 14.28, 3.422, 7.93, 3.478, 0]}, {"time": 3.5333, "x": 14.28, "curve": [3.633, 14.28, 3.733, 0, 3.633, 0, 3.733, 0]}, {"time": 3.8333, "curve": [3.889, 0, 3.944, 6.55, 3.889, 0, 3.944, 7.93]}, {"time": 4, "x": 8.93, "y": 7.93, "curve": [4.056, 11.31, 4.111, 14.28, 4.056, 7.93, 4.111, 0]}, {"time": 4.1667, "x": 14.28, "curve": [4.267, 14.28, 4.367, 0, 4.267, 0, 4.367, 0]}, {"time": 4.4667, "curve": [4.522, 0, 4.578, 6.55, 4.522, 0, 4.578, 7.93]}, {"time": 4.6333, "x": 8.93, "y": 7.93, "curve": [4.689, 11.31, 4.744, 14.28, 4.689, 7.93, 4.744, 0]}, {"time": 4.8, "x": 14.28, "curve": [4.9, 14.28, 5, 0, 4.9, 0, 5, 0]}, {"time": 5.1}]}, "legL_ik": {"translate": [{"curve": [0.111, 0, 0.222, -12.69, 0.111, 0, 0.222, 0]}, {"time": 0.3333, "x": -12.69, "curve": [0.378, -12.69, 0.422, -8.03, 0.378, 0, 0.422, 4.76]}, {"time": 0.4667, "x": -6.15, "y": 4.76, "curve": [0.522, -3.8, 0.578, 0, 0.522, 4.76, 0.578, 0]}, {"time": 0.6333, "curve": [0.744, 0, 0.856, -12.69, 0.744, 0, 0.856, 0]}, {"time": 0.9667, "x": -12.69, "curve": [1.011, -12.69, 1.056, -8.03, 1.011, 0, 1.056, 4.76]}, {"time": 1.1, "x": -6.15, "y": 4.76, "curve": [1.156, -3.8, 1.211, 0, 1.156, 4.76, 1.211, 0]}, {"time": 1.2667, "curve": [1.378, 0, 1.489, -12.69, 1.378, 0, 1.489, 0]}, {"time": 1.6, "x": -12.69, "curve": [1.644, -12.69, 1.689, -8.03, 1.644, 0, 1.689, 4.76]}, {"time": 1.7333, "x": -6.15, "y": 4.76, "curve": [1.789, -3.8, 1.844, 0, 1.789, 4.76, 1.844, 0]}, {"time": 1.9, "curve": [2.011, 0, 2.122, -12.69, 2.011, 0, 2.122, 0]}, {"time": 2.2333, "x": -12.69, "curve": [2.278, -12.69, 2.322, -8.03, 2.278, 0, 2.322, 4.76]}, {"time": 2.3667, "x": -6.15, "y": 4.76, "curve": [2.422, -3.8, 2.478, 0, 2.422, 4.76, 2.478, 0]}, {"time": 2.5333, "curve": "stepped"}, {"time": 2.5667, "curve": [2.678, 0, 2.789, -12.69, 2.678, 0, 2.789, 0]}, {"time": 2.9, "x": -12.69, "curve": [2.944, -12.69, 2.989, -8.03, 2.944, 0, 2.989, 4.76]}, {"time": 3.0333, "x": -6.15, "y": 4.76, "curve": [3.089, -3.8, 3.144, 0, 3.089, 4.76, 3.144, 0]}, {"time": 3.2, "curve": [3.311, 0, 3.422, -12.69, 3.311, 0, 3.422, 0]}, {"time": 3.5333, "x": -12.69, "curve": [3.578, -12.69, 3.622, -8.03, 3.578, 0, 3.622, 4.76]}, {"time": 3.6667, "x": -6.15, "y": 4.76, "curve": [3.722, -3.8, 3.778, 0, 3.722, 4.76, 3.778, 0]}, {"time": 3.8333, "curve": [3.944, 0, 4.056, -12.69, 3.944, 0, 4.056, 0]}, {"time": 4.1667, "x": -12.69, "curve": [4.211, -12.69, 4.256, -8.03, 4.211, 0, 4.256, 4.76]}, {"time": 4.3, "x": -6.15, "y": 4.76, "curve": [4.356, -3.8, 4.411, 0, 4.356, 4.76, 4.411, 0]}, {"time": 4.4667, "curve": [4.578, 0, 4.689, -12.69, 4.578, 0, 4.689, 0]}, {"time": 4.8, "x": -12.69, "curve": [4.844, -12.69, 4.889, -8.03, 4.844, 0, 4.889, 4.76]}, {"time": 4.9333, "x": -6.15, "y": 4.76, "curve": [4.989, -3.8, 5.044, 0, 4.989, 4.76, 5.044, 0]}, {"time": 5.1}]}, "legR": {"translate": [{"x": 0.16, "y": 4.76, "curve": [0.1, 0.16, 0.2, 7.8, 0.1, 4.76, 0.2, -4.6]}, {"time": 0.3, "x": 7.8, "y": -4.6, "curve": "stepped"}, {"time": 0.3333, "x": 7.8, "y": -4.6, "curve": [0.433, 7.8, 0.533, 0.16, 0.433, -4.6, 0.533, 4.76]}, {"time": 0.6333, "x": 0.16, "y": 4.76, "curve": [0.733, 0.16, 0.833, 7.8, 0.733, 4.76, 0.833, -4.6]}, {"time": 0.9333, "x": 7.8, "y": -4.6, "curve": "stepped"}, {"time": 0.9667, "x": 7.8, "y": -4.6, "curve": [1.067, 7.8, 1.167, 0.16, 1.067, -4.6, 1.167, 4.76]}, {"time": 1.2667, "x": 0.16, "y": 4.76, "curve": [1.367, 0.16, 1.467, 7.8, 1.367, 4.76, 1.467, -4.6]}, {"time": 1.5667, "x": 7.8, "y": -4.6, "curve": "stepped"}, {"time": 1.6, "x": 7.8, "y": -4.6, "curve": [1.7, 7.8, 1.8, 0.16, 1.7, -4.6, 1.8, 4.76]}, {"time": 1.9, "x": 0.16, "y": 4.76, "curve": [2, 0.16, 2.1, 7.8, 2, 4.76, 2.1, -4.6]}, {"time": 2.2, "x": 7.8, "y": -4.6, "curve": "stepped"}, {"time": 2.2333, "x": 7.8, "y": -4.6, "curve": [2.333, 7.8, 2.433, 0.16, 2.333, -4.6, 2.433, 4.76]}, {"time": 2.5333, "x": 0.16, "y": 4.76, "curve": "stepped"}, {"time": 2.5667, "x": 0.16, "y": 4.76, "curve": [2.667, 0.16, 2.767, 7.8, 2.667, 4.76, 2.767, -4.6]}, {"time": 2.8667, "x": 7.8, "y": -4.6, "curve": "stepped"}, {"time": 2.9, "x": 7.8, "y": -4.6, "curve": [3, 7.8, 3.1, 0.16, 3, -4.6, 3.1, 4.76]}, {"time": 3.2, "x": 0.16, "y": 4.76, "curve": [3.3, 0.16, 3.4, 7.8, 3.3, 4.76, 3.4, -4.6]}, {"time": 3.5, "x": 7.8, "y": -4.6, "curve": "stepped"}, {"time": 3.5333, "x": 7.8, "y": -4.6, "curve": [3.633, 7.8, 3.733, 0.16, 3.633, -4.6, 3.733, 4.76]}, {"time": 3.8333, "x": 0.16, "y": 4.76, "curve": [3.933, 0.16, 4.033, 7.8, 3.933, 4.76, 4.033, -4.6]}, {"time": 4.1333, "x": 7.8, "y": -4.6, "curve": "stepped"}, {"time": 4.1667, "x": 7.8, "y": -4.6, "curve": [4.267, 7.8, 4.367, 0.16, 4.267, -4.6, 4.367, 4.76]}, {"time": 4.4667, "x": 0.16, "y": 4.76, "curve": [4.567, 0.16, 4.667, 7.8, 4.567, 4.76, 4.667, -4.6]}, {"time": 4.7667, "x": 7.8, "y": -4.6, "curve": "stepped"}, {"time": 4.8, "x": 7.8, "y": -4.6, "curve": [4.9, 7.8, 5, 0.16, 4.9, -4.6, 5, 4.76]}, {"time": 5.1, "x": 0.16, "y": 4.76}]}, "legL": {"translate": [{"x": -0.08, "y": -4.75, "curve": [0.1, -0.08, 0.2, 7.79, 0.1, -4.75, 0.2, 3.19]}, {"time": 0.3, "x": 7.79, "y": 3.19, "curve": "stepped"}, {"time": 0.3333, "x": 7.79, "y": 3.19, "curve": [0.433, 7.79, 0.533, -0.08, 0.433, 3.19, 0.533, -4.75]}, {"time": 0.6333, "x": -0.08, "y": -4.75, "curve": [0.733, -0.08, 0.833, 7.79, 0.733, -4.75, 0.833, 3.19]}, {"time": 0.9333, "x": 7.79, "y": 3.19, "curve": "stepped"}, {"time": 0.9667, "x": 7.79, "y": 3.19, "curve": [1.067, 7.79, 1.167, -0.08, 1.067, 3.19, 1.167, -4.75]}, {"time": 1.2667, "x": -0.08, "y": -4.75, "curve": [1.367, -0.08, 1.467, 7.79, 1.367, -4.75, 1.467, 3.19]}, {"time": 1.5667, "x": 7.79, "y": 3.19, "curve": "stepped"}, {"time": 1.6, "x": 7.79, "y": 3.19, "curve": [1.7, 7.79, 1.8, -0.08, 1.7, 3.19, 1.8, -4.75]}, {"time": 1.9, "x": -0.08, "y": -4.75, "curve": [2, -0.08, 2.1, 7.79, 2, -4.75, 2.1, 3.19]}, {"time": 2.2, "x": 7.79, "y": 3.19, "curve": "stepped"}, {"time": 2.2333, "x": 7.79, "y": 3.19, "curve": [2.333, 7.79, 2.433, -0.08, 2.333, 3.19, 2.433, -4.75]}, {"time": 2.5333, "x": -0.08, "y": -4.75, "curve": "stepped"}, {"time": 2.5667, "x": -0.08, "y": -4.75, "curve": [2.667, -0.08, 2.767, 7.79, 2.667, -4.75, 2.767, 3.19]}, {"time": 2.8667, "x": 7.79, "y": 3.19, "curve": "stepped"}, {"time": 2.9, "x": 7.79, "y": 3.19, "curve": [3, 7.79, 3.1, -0.08, 3, 3.19, 3.1, -4.75]}, {"time": 3.2, "x": -0.08, "y": -4.75, "curve": [3.3, -0.08, 3.4, 7.79, 3.3, -4.75, 3.4, 3.19]}, {"time": 3.5, "x": 7.79, "y": 3.19, "curve": "stepped"}, {"time": 3.5333, "x": 7.79, "y": 3.19, "curve": [3.633, 7.79, 3.733, -0.08, 3.633, 3.19, 3.733, -4.75]}, {"time": 3.8333, "x": -0.08, "y": -4.75, "curve": [3.933, -0.08, 4.033, 7.79, 3.933, -4.75, 4.033, 3.19]}, {"time": 4.1333, "x": 7.79, "y": 3.19, "curve": "stepped"}, {"time": 4.1667, "x": 7.79, "y": 3.19, "curve": [4.267, 7.79, 4.367, -0.08, 4.267, 3.19, 4.367, -4.75]}, {"time": 4.4667, "x": -0.08, "y": -4.75, "curve": [4.567, -0.08, 4.667, 7.79, 4.567, -4.75, 4.667, 3.19]}, {"time": 4.7667, "x": 7.79, "y": 3.19, "curve": "stepped"}, {"time": 4.8, "x": 7.79, "y": 3.19, "curve": [4.9, 7.79, 5, -0.08, 4.9, 3.19, 5, -4.75]}, {"time": 5.1, "x": -0.08, "y": -4.75}]}, "face": {"translatex": [{"value": -5.9, "curve": "stepped"}, {"time": 0.5333, "value": -5.9, "curve": [0.567, -5.89, 0.6, -5.64]}, {"time": 0.6333, "value": -5.64, "curve": [0.667, -5.64, 0.7, -7.53]}, {"time": 0.7333, "value": -7.53, "curve": [0.822, -7.53, 0.911, -4.88]}, {"time": 1, "value": -4.88, "curve": "stepped"}, {"time": 1.8667, "value": -4.88, "curve": "stepped"}, {"time": 2, "value": -4.88, "curve": "stepped"}, {"time": 2.2, "value": -4.88, "curve": "stepped"}, {"time": 3.3333, "value": -4.88, "curve": "stepped"}, {"time": 3.4333, "value": -4.88, "curve": [3.478, -4.88, 3.522, -7.38]}, {"time": 3.5667, "value": -7.38, "curve": [3.6, -7.38, 3.633, -6.39]}, {"time": 3.6667, "value": -5.9}], "translatey": [{"value": 0.2, "curve": "stepped"}, {"time": 0.5333, "value": 0.2, "curve": [0.567, 0.39, 0.6, 0.76]}, {"time": 0.6333, "value": 0.76, "curve": [0.667, 0.76, 0.7, -1.71]}, {"time": 0.7333, "value": -2.71, "curve": [0.822, -5.38, 0.911, -7.83]}, {"time": 1, "value": -7.83, "curve": "stepped"}, {"time": 1.8667, "value": -7.83, "curve": [1.911, -8.34, 1.956, -9.36]}, {"time": 2, "value": -9.36, "curve": [2.067, -9.36, 2.133, -2.98]}, {"time": 2.2, "value": -1.73, "curve": "stepped"}, {"time": 3.3333, "value": -1.73, "curve": [3.367, -0.17, 3.4, 2.97]}, {"time": 3.4333, "value": 2.97, "curve": [3.511, 2.97, 3.589, 1.12]}, {"time": 3.6667, "value": 0.2}]}, "eye2": {"translate": [{"curve": "stepped"}, {"time": 0.6667, "curve": "stepped"}, {"time": 3.2333}]}, "brow_m": {"translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": [0.2, 0, 0.233, -2.87, 0.2, 0, 0.233, 0.07]}, {"time": 0.2667, "x": -2.87, "y": 0.07, "curve": [0.322, -2.87, 0.378, 0, 0.322, 0.07, 0.378, 0]}, {"time": 0.4333, "curve": "stepped"}, {"time": 2.0667, "curve": [2.1, 0, 2.133, -2.87, 2.1, 0, 2.133, 0.07]}, {"time": 2.1667, "x": -2.87, "y": 0.07, "curve": [2.222, -2.87, 2.278, 0, 2.222, 0.07, 2.278, 0]}, {"time": 2.3333, "curve": "stepped"}, {"time": 3.9667, "curve": [4, 0, 4.033, -2.87, 4, 0, 4.033, 0.07]}, {"time": 4.0667, "x": -2.87, "y": 0.07, "curve": [4.122, -2.87, 4.178, 0, 4.122, 0.07, 4.178, 0]}, {"time": 4.2333}]}, "body_cntrl": {"rotate": [{"curve": "stepped"}, {"time": 0.5333, "curve": [0.567, 0, 0.6, -0.72]}, {"time": 0.6333, "value": -0.72, "curve": [0.689, -0.72, 0.744, 2.13]}, {"time": 0.8, "value": 2.13, "curve": "stepped"}, {"time": 1.8667, "value": 2.13, "curve": [1.911, 2.13, 1.956, 2.54]}, {"time": 2, "value": 2.54, "curve": [2.044, 2.54, 2.089, -1.61]}, {"time": 2.1333, "value": -1.61, "curve": "stepped"}, {"time": 3.4333, "value": -1.61, "curve": [3.489, -1.61, 3.544, 0]}, {"time": 3.6}]}, "eye_up_closed": {"translate": [{"x": 35.51, "curve": "stepped"}, {"time": 0.1333, "x": 35.51, "curve": [0.178, 23.68, 0.222, -4.47, 0.178, 0, 0.222, 0]}, {"time": 0.2667, "x": -4.47, "curve": "stepped"}, {"time": 0.3, "x": -4.47, "curve": [0.311, -4.47, 0.378, 23.68, 0.311, 0, 0.378, 0]}, {"time": 0.4333, "x": 35.51, "curve": "stepped"}, {"time": 2.0333, "x": 35.51, "curve": [2.078, 23.68, 2.122, -4.47, 2.078, 0, 2.122, 0]}, {"time": 2.1667, "x": -4.47, "curve": "stepped"}, {"time": 2.2, "x": -4.47, "curve": [2.211, -4.47, 2.278, 23.68, 2.211, 0, 2.278, 0]}, {"time": 2.3333, "x": 35.51, "curve": "stepped"}, {"time": 3.9333, "x": 35.51, "curve": [3.978, 23.68, 4.022, -4.47, 3.978, 0, 4.022, 0]}, {"time": 4.0667, "x": -4.47, "curve": "stepped"}, {"time": 4.1, "x": -4.47, "curve": [4.111, -4.47, 4.178, 23.68, 4.111, 0, 4.178, 0]}, {"time": 4.2333, "x": 35.51}]}, "brow": {"translate": [{"x": -5.41}], "scale": [{"y": 0.705}]}, "pupil": {"translate": [{"x": -7.35, "curve": "stepped"}, {"time": 0.2667, "x": -7.35, "curve": [0.278, -7.33, 0.289, -7.27, 0.278, -2.45, 0.289, -7.35]}, {"time": 0.3, "x": -7.27, "y": -7.35, "curve": "stepped"}, {"time": 0.6667, "x": -7.27, "y": -7.35, "curve": [0.678, -7.27, 0.689, -6.85, 0.678, -7.35, 0.689, 3.03]}, {"time": 0.7, "x": -6.85, "y": 3.03, "curve": "stepped"}, {"time": 1.1667, "x": -6.85, "y": 3.03, "curve": [1.178, -6.85, 1.189, -6.61, 1.178, 3.03, 1.189, 7.47]}, {"time": 1.2, "x": -6.61, "y": 7.47, "curve": "stepped"}, {"time": 1.6667, "x": -6.61, "y": 7.47, "curve": [1.678, -6.61, 1.689, -6.7, 1.678, 7.47, 1.689, 2.23]}, {"time": 1.7, "x": -6.7, "y": 2.23, "curve": "stepped"}, {"time": 2.1667, "x": -6.7, "y": 2.23, "curve": [2.178, -6.7, 2.189, -7.43, 2.178, 2.23, 2.189, 2.2]}, {"time": 2.2, "x": -7.43, "y": 2.2, "curve": "stepped"}, {"time": 2.7, "x": -7.43, "y": 2.2, "curve": [2.711, -7.43, 2.722, -7.4, 2.711, 2.2, 2.722, -3.8]}, {"time": 2.7333, "x": -7.4, "y": -3.8, "curve": "stepped"}, {"time": 3.3667, "x": -7.4, "y": -3.8, "curve": [3.389, -7.4, 3.411, -7.76, 3.389, -3.8, 3.411, 15.55]}, {"time": 3.4333, "x": -7.76, "y": 15.55, "curve": "stepped"}, {"time": 4.0667, "x": -7.76, "y": 15.55, "curve": [4.078, -7.76, 4.089, -11.43, 4.078, 15.55, 4.089, 16.28]}, {"time": 4.1, "x": -11.43, "y": 16.28, "curve": "stepped"}, {"time": 4.6667, "x": -11.43, "y": 16.28, "curve": [4.678, -11.43, 4.689, -8.71, 4.678, 16.28, 4.689, 5.43]}, {"time": 4.7, "x": -7.35}]}}}}}